import{D as k}from"./DashboardLayout-HXqH_ZDc.js";import{D as B}from"./DataTable-BdcmlClz.js";import{B as E}from"./BuildingForm-BCC_xiW2.js";import{N as S}from"./Notification-II7oLsx2.js";import{_ as M,i as $,h as b,f as l,r as m,o as r,c as h,b as f,e as a,a as t,t as o,g}from"./app-B62tIoiF.js";const C={mixins:[$],components:{DashboardLayout:k,DataTable:B,BuildingForm:E,Notification:S},data(){return{loading:!1,buildings:[],selectedBuilding:null,showEditModal:!1,showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:"",user:null,isSuperAdmin:!1,userLoaded:!1,columns:[]}},computed:{totalBuildings(){return this.buildings.length},activeBuildings(){return this.buildings.filter(e=>e.admin_count>0).length},averageMonthlyFee(){return this.buildings.length===0?0:this.buildings.reduce((s,d)=>s+parseFloat(d.monthly_fee||0),0)/this.buildings.length}},async mounted(){this.initializeUserFromStorage(),this.initializeColumns(),await this.fetchUser(),this.isSuperAdmin&&this.loadBuildings()},methods:{initializeColumns(){this.columns=[{key:"name",label:this.$t("building_name")},{key:"city",label:this.$t("city")},{key:"monthly_fee_display",label:this.$t("monthly_fee")},{key:"admin_count",label:this.$t("admins")},{key:"neighbor_count",label:this.$t("neighbors")}]},initializeUserFromStorage(){try{const e=JSON.parse(localStorage.getItem("user")||"null");e&&e.role&&(this.isSuperAdmin=e.role==="super_admin",this.userLoaded=!0)}catch(e){console.error("Error parsing user data from localStorage:",e)}},async fetchUser(){try{const e=await this.$axios.get("/user");this.user=e.data,this.isSuperAdmin=this.user.role==="super_admin",this.userLoaded=!0,localStorage.setItem("user",JSON.stringify(this.user))}catch(e){console.error("Error fetching user:",e),this.$router.push("/login")}},async loadBuildings(){this.loading=!0;try{const e=await this.$axios.get("/buildings"),s=await this.$axios.get("/admin/users"),d=s.data.data||s.data;this.buildings=e.data.map(c=>({...c,monthly_fee_display:`₪${parseFloat(c.monthly_fee||0).toFixed(2)}`,admin_count:d.filter(i=>i.building_id===c.id&&i.role==="admin").length,neighbor_count:d.filter(i=>i.building_id===c.id&&i.role==="neighbor").length}))}catch(e){console.error("Error loading buildings:",e),this.showError(this.$t("error"),this.$t("failed_load_buildings"))}finally{this.loading=!1}},editBuilding(e){this.selectedBuilding=e,this.showEditModal=!0},closeEditModal(){this.showEditModal=!1,this.selectedBuilding=null},handleEditSuccess(e){this.showSuccess(this.$t("success"),this.$t("building_updated")),this.closeEditModal(),this.loadBuildings()},handleEditError(e){this.showError(this.$t("error"),e)},async deleteBuilding(e){var s,d;if(confirm(this.$t("confirm_delete_building",{name:e.name})))try{await this.$axios.delete(`/buildings/${e.id}`),this.showSuccess(this.$t("success"),this.$t("building_deleted")),this.loadBuildings()}catch(c){this.showError(this.$t("error"),((d=(s=c.response)==null?void 0:s.data)==null?void 0:d.message)||this.$t("failed_delete_building"))}},showSuccess(e,s){this.notificationType="success",this.notificationTitle=e,this.notificationMessage=s,this.showNotification=!0},showError(e,s){this.notificationType="error",this.notificationTitle=e,this.notificationMessage=s,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},N={key:0},T={key:1,class:"text-gray-500 text-sm"},F={key:0,class:"text-center py-4"},A={key:1},L={class:"mb-6 flex justify-between items-center"},j={class:"text-2xl font-bold text-gray-900"},D={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"},U={class:"bg-white p-6 rounded-lg shadow-sm"},z={class:"text-lg font-semibold text-gray-700"},V={class:"text-3xl font-bold text-blue-600"},H={class:"bg-white p-6 rounded-lg shadow-sm"},I={class:"text-lg font-semibold text-gray-700"},J={class:"text-3xl font-bold text-green-600"},O={class:"bg-white p-6 rounded-lg shadow-sm"},R={class:"text-lg font-semibold text-gray-700"},q={class:"text-3xl font-bold text-purple-600"},G={class:"flex space-x-2"},K=["onClick"],P=["onClick"],Q={key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center"},W={class:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4"},X={class:"flex justify-between items-center p-6 border-b"},Y={class:"text-xl font-semibold text-gray-900"},Z={class:"p-6"},ee={key:3,class:"text-center py-8"},te={class:"text-red-500"};function ie(e,s,d,c,i,n){const u=m("router-link"),y=m("data-table"),p=m("building-form"),x=m("notification"),w=m("dashboard-layout");return r(),b(w,{title:e.$t("building_management")},{sidebar:l(()=>[i.userLoaded?(r(),h("div",N,[a(u,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:l(()=>[g(o(e.$t("expenses")),1)]),_:1}),a(u,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:l(()=>[g(o(e.$t("incomes")),1)]),_:1}),a(u,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:l(()=>[g(o(e.$t("users")),1)]),_:1}),i.isSuperAdmin?(r(),b(u,{key:0,to:"/admin/buildings",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:l(()=>[g(o(e.$t("buildings")),1)]),_:1})):f("",!0),i.isSuperAdmin?f("",!0):(r(),b(u,{key:1,to:"/admin/my-building",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:l(()=>[g(o(e.$t("my_building")),1)]),_:1}))])):(r(),h("div",T,o(e.$t("loading_menu")),1))]),default:l(()=>[i.loading&&!i.userLoaded?(r(),h("div",F,[t("p",null,o(e.$t("loading")),1)])):i.isSuperAdmin?(r(),h("div",A,[t("div",L,[t("h2",j,o(e.$t("building_management")),1),a(u,{to:"/admin/buildings/create",class:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"},{default:l(()=>[s[1]||(s[1]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1)),g(" "+o(e.$t("add_building")),1)]),_:1,__:[1]})]),t("div",D,[t("div",U,[t("h3",z,o(e.$t("total_buildings")),1),t("p",V,o(n.totalBuildings),1)]),t("div",H,[t("h3",I,o(e.$t("active_buildings")),1),t("p",J,o(n.activeBuildings),1)]),t("div",O,[t("h3",R,o(e.$t("average_monthly_fee")),1),t("p",q,"₪"+o(n.averageMonthlyFee.toFixed(2)),1)])]),a(y,{title:e.$t("buildings"),columns:i.columns,items:i.buildings,loading:i.loading},{actions:l(({item:_})=>[t("div",G,[t("button",{onClick:v=>n.editBuilding(_),class:"text-indigo-600 hover:text-indigo-900"},o(e.$t("edit")),9,K),t("button",{onClick:v=>n.deleteBuilding(_),class:"text-red-600 hover:text-red-900"},o(e.$t("delete")),9,P)])]),"header-actions":l(()=>[a(u,{to:"/admin/buildings/create",class:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"},{default:l(()=>[s[2]||(s[2]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1)),g(" "+o(e.$t("add_building")),1)]),_:1,__:[2]})]),_:1},8,["title","columns","items","loading"])])):f("",!0),i.showEditModal?(r(),h("div",Q,[t("div",W,[t("div",X,[t("h2",Y,o(e.$t("edit_building")),1),t("button",{onClick:s[0]||(s[0]=(..._)=>n.closeEditModal&&n.closeEditModal(..._)),class:"text-gray-400 hover:text-gray-600"},s[3]||(s[3]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",Z,[a(p,{building:i.selectedBuilding,"is-edit":!0,"is-my-building":!1,onSuccess:n.handleEditSuccess,onError:n.handleEditError,onCancel:n.closeEditModal},null,8,["building","onSuccess","onError","onCancel"])])])])):f("",!0),a(x,{show:i.showNotification,type:i.notificationType,title:i.notificationTitle,message:i.notificationMessage,onClose:n.closeNotification},null,8,["show","type","title","message","onClose"]),!i.isSuperAdmin&&i.userLoaded?(r(),h("div",ee,[t("p",te,o(e.$t("no_permission")),1)])):f("",!0)]),_:1},8,["title"])}const de=M(C,[["render",ie]]);export{de as default};
