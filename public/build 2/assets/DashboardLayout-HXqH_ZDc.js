import{_ as a,c,a as s,t as d,m as e,o as i}from"./app-B62tIoiF.js";const n={name:"DashboardLayout",props:{title:{type:String,required:!0}}},r={class:"min-h-screen bg-gray-100"},l={class:"flex"},_={class:"w-64 bg-white shadow-lg h-screen fixed"},h={class:"p-4"},p={class:"text-xl font-bold text-gray-800 mb-6"},m={class:"space-y-2"},f={class:"ml-64 flex-1 p-8"},u={class:"max-w-7xl mx-auto"},x={class:"bg-white rounded-lg shadow p-6"};function g(t,b,o,v,y,w){return i(),c("div",r,[s("div",l,[s("div",_,[s("div",h,[s("h2",p,d(o.title),1),s("nav",m,[e(t.$slots,"sidebar")])])]),s("div",f,[s("div",u,[s("div",x,[e(t.$slots,"default")])])])])])}const D=a(n,[["render",g]]);export{D};
