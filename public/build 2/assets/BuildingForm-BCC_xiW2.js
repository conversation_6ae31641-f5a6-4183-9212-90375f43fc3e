import{_ as f,i as b,c as m,b as p,a as e,t as r,w as n,v as l,d as y,o as c}from"./app-B62tIoiF.js";const g={name:"BuildingForm",mixins:[b],props:{building:{type:Object,default:()=>({name:"",address:"",city:"",country:"",postal_code:"",description:"",monthly_fee:70})},isEdit:{type:Boolean,default:!1},isMyBuilding:{type:Boolean,default:!1}},data(){return{processing:!1,formData:{name:"",address:"",city:"",country:"",postal_code:"",description:"",monthly_fee:70}}},watch:{building:{handler(o){o&&(this.formData={name:o.name||"",address:o.address||"",city:o.city||"",country:o.country||"",postal_code:o.postal_code||"",description:o.description||"",monthly_fee:o.monthly_fee||70})},immediate:!0,deep:!0}},mounted(){this.building&&(this.formData={name:this.building.name||"",address:this.building.address||"",city:this.building.city||"",country:this.building.country||"",postal_code:this.building.postal_code||"",description:this.building.description||"",monthly_fee:this.building.monthly_fee||70})},methods:{async handleSubmit(){var o,t;this.processing=!0;try{const d={...this.formData};let u,s;this.isEdit?this.isMyBuilding?(u="/my-building",s="put"):(u=`/buildings/${this.building.id}`,s="put"):(u="/buildings",s="post");const a=await this.$axios[s](u,d);this.$emit("success",a.data)}catch(d){this.$emit("error",((t=(o=d.response)==null?void 0:o.data)==null?void 0:t.message)||this.$t("failed_save_building"))}finally{this.processing=!1}}}},h={key:0,class:"text-xl font-semibold text-gray-900 mb-6"},x={for:"name",class:"block text-sm font-medium text-gray-700 mb-2"},_={for:"address",class:"block text-sm font-medium text-gray-700 mb-2"},D={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},v={for:"city",class:"block text-sm font-medium text-gray-700 mb-2"},w={for:"postal_code",class:"block text-sm font-medium text-gray-700 mb-2"},k={for:"country",class:"block text-sm font-medium text-gray-700 mb-2"},V={for:"monthly_fee",class:"block text-sm font-medium text-gray-700 mb-2"},U={class:"mt-1 text-sm text-gray-500"},E={for:"description",class:"block text-sm font-medium text-gray-700 mb-2"},M={class:"flex justify-end space-x-3 pt-4"},S=["disabled"];function $(o,t,d,u,s,a){return c(),m("div",null,[d.isEdit?p("",!0):(c(),m("h2",h,r(o.$t("building_information")),1)),e("form",{onSubmit:t[8]||(t[8]=y((...i)=>a.handleSubmit&&a.handleSubmit(...i),["prevent"])),class:"space-y-6"},[e("div",null,[e("label",x,r(o.$t("building_name")),1),n(e("input",{type:"text",id:"name","onUpdate:modelValue":t[0]||(t[0]=i=>s.formData.name=i),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},null,512),[[l,s.formData.name]])]),e("div",null,[e("label",_,r(o.$t("address")),1),n(e("input",{type:"text",id:"address","onUpdate:modelValue":t[1]||(t[1]=i=>s.formData.address=i),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[l,s.formData.address]])]),e("div",D,[e("div",null,[e("label",v,r(o.$t("city")),1),n(e("input",{type:"text",id:"city","onUpdate:modelValue":t[2]||(t[2]=i=>s.formData.city=i),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[l,s.formData.city]])]),e("div",null,[e("label",w,r(o.$t("postal_code")),1),n(e("input",{type:"text",id:"postal_code","onUpdate:modelValue":t[3]||(t[3]=i=>s.formData.postal_code=i),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[l,s.formData.postal_code]])])]),e("div",null,[e("label",k,r(o.$t("country")),1),n(e("input",{type:"text",id:"country","onUpdate:modelValue":t[4]||(t[4]=i=>s.formData.country=i),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[l,s.formData.country]])]),e("div",null,[e("label",V,r(o.$t("monthly_fee"))+" (₪)",1),n(e("input",{type:"number",id:"monthly_fee","onUpdate:modelValue":t[5]||(t[5]=i=>s.formData.monthly_fee=i),step:"0.01",min:"0",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},null,512),[[l,s.formData.monthly_fee]]),e("p",U,r(o.$t("monthly_fee_description")),1)]),e("div",null,[e("label",E,r(o.$t("description")),1),n(e("textarea",{id:"description","onUpdate:modelValue":t[6]||(t[6]=i=>s.formData.description=i),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[l,s.formData.description]])]),e("div",M,[e("button",{type:"button",onClick:t[7]||(t[7]=i=>o.$emit("cancel")),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"},r(o.$t("cancel")),1),e("button",{type:"submit",disabled:s.processing,class:"px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"},r(s.processing?o.$t("saving"):d.isEdit?o.$t("update_building"):o.$t("create_building")),9,S)])],32)])}const j=f(g,[["render",$]]);export{j as B};
