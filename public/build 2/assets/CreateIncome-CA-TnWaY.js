import{D as m}from"./DashboardLayout-HXqH_ZDc.js";import{I as u}from"./IncomeForm-CTQWsuWp.js";import{N as p}from"./Notification-II7oLsx2.js";import{_ as g,h as _,f as t,r as a,o as x,a as l,e as i,g as r}from"./app-B62tIoiF.js";const y={components:{DashboardLayout:m,IncomeForm:u,Notification:p},data(){return{showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:""}},methods:{handleSuccess(){this.showSuccess("Income Recorded","The income has been recorded successfully."),this.$router.push("/admin/incomes")},handleError(e){this.showError("Recording Failed",e)},handleCancel(){this.$router.push("/admin/incomes")},showSuccess(e,o){this.notificationType="success",this.notificationTitle=e,this.notificationMessage=o,this.showNotification=!0},showError(e,o){this.notificationType="error",this.notificationTitle=e,this.notificationMessage=o,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},b={class:"max-w-2xl mx-auto"},w={class:"bg-white p-6 rounded-lg shadow-sm"};function N(e,o,T,v,s,n){const c=a("router-link"),d=a("income-form"),f=a("notification"),h=a("dashboard-layout");return x(),_(h,{title:"Record Income"},{sidebar:t(()=>[i(c,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>o[0]||(o[0]=[r(" All Incomes ")])),_:1,__:[0]}),i(c,{to:"/admin/incomes/create",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>o[1]||(o[1]=[r(" Record Income ")])),_:1,__:[1]}),i(c,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>o[2]||(o[2]=[r(" Expenses ")])),_:1,__:[2]})]),default:t(()=>[l("div",b,[l("div",w,[i(d,{onSuccess:n.handleSuccess,onError:n.handleError,onCancel:n.handleCancel},null,8,["onSuccess","onError","onCancel"])])]),i(f,{show:s.showNotification,type:s.notificationType,title:s.notificationTitle,message:s.notificationMessage,onClose:n.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1})}const S=g(y,[["render",N]]);export{S as default};
