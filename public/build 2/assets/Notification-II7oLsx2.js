import{_ as c,c as d,b as u,a as e,h as m,n as l,p as f,t as i,o as a}from"./app-B62tIoiF.js";const h={name:"Notification",props:{show:{type:Boolean,default:!1},type:{type:String,default:"info",validator:s=>["success","error","warning","info"].includes(s)},title:{type:String,required:!0},message:{type:String,required:!0},duration:{type:Number,default:5e3}},data(){return{timer:null}},computed:{typeClasses(){return{success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"}[this.type]},iconClasses(){return{success:"text-green-400",error:"text-red-400",warning:"text-yellow-400",info:"text-blue-400"}[this.type]},textClasses(){return{success:"text-green-800",error:"text-red-800",warning:"text-yellow-800",info:"text-blue-800"}[this.type]},icon(){return{success:"CheckCircleIcon",error:"XCircleIcon",warning:"ExclamationIcon",info:"InformationCircleIcon"}[this.type]}},watch:{show(s){s&&this.duration>0&&this.startTimer()}},methods:{close(){this.$emit("close")},startTimer(){this.timer&&clearTimeout(this.timer),this.timer=setTimeout(()=>{this.close()},this.duration)}},beforeDestroy(){this.timer&&clearTimeout(this.timer)}},x={class:"p-4"},y={class:"flex items-start"},g={class:"flex-shrink-0"},w={class:"ml-3 w-0 flex-1"},p={class:"ml-4 flex-shrink-0 flex"};function C(s,r,o,b,_,t){return o.show?(a(),d("div",{key:0,class:l(["fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg overflow-hidden",t.typeClasses])},[e("div",x,[e("div",y,[e("div",g,[(a(),m(f(t.icon),{class:l(["h-6 w-6",t.iconClasses]),"aria-hidden":"true"},null,8,["class"]))]),e("div",w,[e("p",{class:l(["text-sm font-medium",t.textClasses])},i(o.title),3),e("p",{class:l(["mt-1 text-sm",t.textClasses])},i(o.message),3)]),e("div",p,[e("button",{onClick:r[0]||(r[0]=(...n)=>t.close&&t.close(...n)),class:"inline-flex text-gray-400 hover:text-gray-500 focus:outline-none"},r[1]||(r[1]=[e("span",{class:"sr-only"},"Close",-1),e("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))])])])],2)):u("",!0)}const k=c(h,[["render",C]]);export{k as N};
