import{D as p}from"./DashboardLayout-HXqH_ZDc.js";import{D as g}from"./DataTable-BdcmlClz.js";import{N as u}from"./Notification-II7oLsx2.js";import{_,i as f,h as x,f as m,r,o as b,a as s,e as c,t as a,n as w}from"./app-B62tIoiF.js";const E={mixins:[f],components:{DashboardLayout:p,DataTable:g,Notification:u},data(){return{loading:!1,myExpenses:[],myIncomes:[],showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:"",expenseColumns:[],incomeColumns:[]}},computed:{totalExpenses(){return this.myExpenses.reduce((t,o)=>t+parseFloat(o.amount),0)},totalPayments(){return this.myIncomes.reduce((t,o)=>t+parseFloat(o.amount),0)},outstandingBalance(){return this.totalExpenses-this.totalPayments}},async created(){this.initializeColumns(),await this.loadDashboardData()},methods:{initializeColumns(){this.expenseColumns=[{key:"expense_type.name",label:this.$t("type")},{key:"amount",label:this.$t("amount")},{key:"month",label:this.$t("month")},{key:"year",label:this.$t("year")},{key:"notes",label:this.$t("notes")}],this.incomeColumns=[{key:"amount",label:this.$t("amount")},{key:"payment_date",label:this.$t("payment_date")},{key:"payment_method",label:this.$t("method")},{key:"notes",label:this.$t("notes")}]},async loadDashboardData(){var t,o;this.loading=!0;try{const i=JSON.parse(localStorage.getItem("user")||"{}");if(console.log("Current user:",i),!i.id){this.showError(this.$t("authentication_error"),this.$t("user_not_found"));return}console.log("Loading expenses for user ID:",i.id);const n=await this.$axios.get("/expenses",{params:{user_id:i.id}});console.log("Expenses response:",n.data),n.data.data?this.myExpenses=n.data.data:Array.isArray(n.data)?this.myExpenses=n.data:this.myExpenses=[],console.log("My expenses:",this.myExpenses),console.log("Loading incomes for user ID:",i.id);const e=await this.$axios.get("/incomes",{params:{user_id:i.id}});console.log("Incomes response:",e.data),e.data.data?this.myIncomes=e.data.data:Array.isArray(e.data)?this.myIncomes=e.data:this.myIncomes=[],console.log("My incomes:",this.myIncomes),this.myExpenses.length===0&&this.myIncomes.length===0&&this.showSuccess(this.$t("data_loaded"),this.$t("no_financial_records"))}catch(i){console.error("Error loading dashboard data:",i),this.showError(this.$t("error_loading_dashboard"),((o=(t=i.response)==null?void 0:t.data)==null?void 0:o.message)||this.$t("failed_load_financial_data"))}finally{this.loading=!1}},showSuccess(t,o){this.notificationType="success",this.notificationTitle=t,this.notificationMessage=o,this.showNotification=!0},showError(t,o){this.notificationType="error",this.notificationTitle=t,this.notificationMessage=o,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},C={class:"block px-4 py-2 text-gray-600 bg-indigo-50 text-indigo-600 rounded"},N={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"},k={class:"bg-white p-6 rounded-lg shadow-sm"},D={class:"text-lg font-semibold text-gray-700"},I={class:"text-3xl font-bold text-red-600"},$={class:"bg-white p-6 rounded-lg shadow-sm"},T={class:"text-lg font-semibold text-gray-700"},v={class:"text-3xl font-bold text-green-600"},B={class:"bg-white p-6 rounded-lg shadow-sm"},M={class:"text-lg font-semibold text-gray-700"},F={class:"mt-6"};function S(t,o,i,n,e,l){const d=r("data-table"),h=r("notification"),y=r("dashboard-layout");return b(),x(y,{title:t.$t("neighbor_dashboard")},{sidebar:m(()=>[s("div",C,a(t.$t("my_financial_summary")),1)]),default:m(()=>[s("div",N,[s("div",k,[s("h3",D,a(t.$t("total_expenses")),1),s("p",I,"₪"+a(l.totalExpenses.toFixed(2)),1)]),s("div",$,[s("h3",T,a(t.$t("total_payments")),1),s("p",v,"₪"+a(l.totalPayments.toFixed(2)),1)]),s("div",B,[s("h3",M,a(t.$t("outstanding_balance")),1),s("p",{class:w(["text-3xl font-bold",l.outstandingBalance>0?"text-red-600":l.outstandingBalance<0?"text-green-600":"text-gray-600"])}," ₪"+a(l.outstandingBalance.toFixed(2)),3)])]),c(d,{title:t.$t("my_expenses"),columns:e.expenseColumns,items:e.myExpenses,loading:e.loading},null,8,["title","columns","items","loading"]),s("div",F,[c(d,{title:t.$t("my_income_records"),columns:e.incomeColumns,items:e.myIncomes,loading:e.loading},null,8,["title","columns","items","loading"])]),c(h,{show:e.showNotification,type:e.notificationType,title:e.notificationTitle,message:e.notificationMessage,onClose:l.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1},8,["title"])}const R=_(E,[["render",S]]);export{R as default};
