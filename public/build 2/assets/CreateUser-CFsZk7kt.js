import{D as g}from"./DashboardLayout-HXqH_ZDc.js";import{U as f}from"./UserForm-oXxcTwhj.js";import{N as m}from"./Notification-II7oLsx2.js";import{_ as p,h as _,f as t,r as a,o as b,a as d,e as o,g as c}from"./app-B62tIoiF.js";const y={components:{DashboardLayout:g,UserForm:f,Notification:m},data(){return{buildings:[],user:null,isSuperAdmin:!1,adminBuildingId:null,showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:""}},async mounted(){await this.fetchUser(),await this.fetchBuildings()},methods:{async fetchUser(){try{const i=await this.$axios.get("/user");this.user=i.data,this.isSuperAdmin=this.user.role==="super_admin",this.adminBuildingId=this.user.building_id}catch(i){console.error("Error fetching user:",i),this.$router.push("/login")}},async fetchBuildings(){try{const i=await this.$axios.get("/buildings");this.buildings=i.data}catch(i){console.error("Error fetching buildings:",i)}},handleSuccess(i){this.showSuccess("Success","User created successfully"),setTimeout(()=>{this.$router.push("/admin/users")},1500)},handleError(i){this.showError("Error",i)},handleCancel(){this.$router.push("/admin/users")},showSuccess(i,s){this.notificationType="success",this.notificationTitle=i,this.notificationMessage=s,this.showNotification=!0},showError(i,s){this.notificationType="error",this.notificationTitle=i,this.notificationMessage=s,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},x={class:"max-w-2xl mx-auto"},w={class:"bg-white p-6 rounded-lg shadow-sm"};function N(i,s,v,C,e,n){const r=a("router-link"),l=a("user-form"),u=a("notification"),h=a("dashboard-layout");return b(),_(h,{title:"Create User"},{sidebar:t(()=>[o(r,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>s[0]||(s[0]=[c(" All Users ")])),_:1,__:[0]}),o(r,{to:"/admin/users/create",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>s[1]||(s[1]=[c(" Create User ")])),_:1,__:[1]}),o(r,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>s[2]||(s[2]=[c(" Expenses ")])),_:1,__:[2]}),o(r,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>s[3]||(s[3]=[c(" Incomes ")])),_:1,__:[3]})]),default:t(()=>[d("div",x,[d("div",w,[o(l,{"is-super-admin":e.isSuperAdmin,"admin-building-id":e.adminBuildingId,buildings:e.buildings,onSuccess:n.handleSuccess,onError:n.handleError,onCancel:n.handleCancel},null,8,["is-super-admin","admin-building-id","buildings","onSuccess","onError","onCancel"])])]),o(u,{show:e.showNotification,type:e.notificationType,title:e.notificationTitle,message:e.notificationMessage,onClose:n.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1})}const k=p(y,[["render",N]]);export{k as default};
