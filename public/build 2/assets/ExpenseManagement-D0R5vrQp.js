import{D as M}from"./DashboardLayout-HXqH_ZDc.js";import{D as S}from"./DataTable-BdcmlClz.js";import{E as F}from"./ExpenseForm-B2fnylaP.js";import{N as $}from"./Notification-II7oLsx2.js";import{_ as C,i as P,h as y,f as d,r as m,o as h,a as t,e as c,c as f,b,w as x,t as s,j as _,v as N,g as p}from"./app-B62tIoiF.js";const D={mixins:[P],components:{DashboardLayout:M,DataTable:S,ExpenseForm:F,Notification:$},data(){return{loading:!1,generatingExpenses:!1,expenses:[],selectedExpense:null,showEditModal:!1,showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:"",user:null,isSuperAdmin:!1,userLoaded:!1,filters:{type:"",month:"",year:new Date().getFullYear()},pagination:{currentPage:1,lastPage:1,from:0,to:0,total:0},columns:[]}},computed:{totalExpenses(){return this.expenses.reduce((e,o)=>e+parseFloat(o.amount),0)},thisMonthExpenses(){const e=new Date().getMonth()+1,o=new Date().getFullYear();return this.expenses.filter(l=>parseInt(l.month)===e&&parseInt(l.year)===o).reduce((l,g)=>l+parseFloat(g.amount),0)},automaticExpenses(){return this.expenses.filter(e=>e.is_automatic===!0||e.is_automatic===1).reduce((e,o)=>e+parseFloat(o.amount),0)},manualExpenses(){return this.expenses.filter(e=>e.is_automatic===!1||e.is_automatic===0).reduce((e,o)=>e+parseFloat(o.amount),0)}},async created(){this.initializeColumns(),this.initializeUserFromStorage(),await this.fetchUser(),this.loadExpenses()},methods:{initializeColumns(){this.columns=[{key:"expense_type.name",label:this.$t("type")},{key:"user.name",label:this.$t("neighbor")},{key:"user.apartment_number",label:this.$t("apartment")},{key:"month",label:this.$t("month")},{key:"year",label:this.$t("year")},{key:"amount",label:this.$t("amount")},{key:"is_automatic",label:this.$t("auto")},{key:"notes",label:this.$t("notes")}]},initializeUserFromStorage(){try{const e=JSON.parse(localStorage.getItem("user")||"null");e&&e.role&&(this.isSuperAdmin=e.role==="super_admin",this.userLoaded=!0)}catch(e){console.error("Error parsing user data from localStorage:",e)}},async fetchUser(){try{const e=await this.$axios.get("/user");this.user=e.data,this.isSuperAdmin=this.user.role==="super_admin",this.userLoaded=!0,localStorage.setItem("user",JSON.stringify(this.user))}catch(e){console.error("Error fetching user:",e)}},async loadExpenses(){this.loading=!0;try{const e=await this.$axios.get("/expenses",{params:{page:this.pagination.currentPage,...this.filters}});this.expenses=e.data.data,this.pagination={currentPage:e.data.current_page,lastPage:e.data.last_page,from:e.data.from,to:e.data.to,total:e.data.total}}catch{this.showError("Error loading expenses")}finally{this.loading=!1}},applyFilters(){this.pagination.currentPage=1,this.loadExpenses()},previousPage(){this.pagination.currentPage>1&&(this.pagination.currentPage--,this.loadExpenses())},nextPage(){this.pagination.currentPage<this.pagination.lastPage&&(this.pagination.currentPage++,this.loadExpenses())},editExpense(e){this.selectedExpense=e,this.showEditModal=!0},async deleteExpense(e){if(confirm("Are you sure you want to delete this expense?"))try{await this.$axios.delete(`/expenses/${e.id}`),this.showSuccess("Deleted","Expense deleted successfully"),this.loadExpenses()}catch{this.showError("Delete Failed","Failed to delete expense")}},handleEditSuccess(){this.showSuccess("Updated","Expense updated successfully"),this.closeEditModal(),this.loadExpenses()},handleEditError(e){this.showError("Update Failed",e)},closeEditModal(){this.showEditModal=!1,this.selectedExpense=null},showSuccess(e,o){this.notificationType="success",this.notificationTitle=e,this.notificationMessage=o,this.showNotification=!0},showError(e,o){this.notificationType="error",this.notificationTitle=e,this.notificationMessage=o,this.showNotification=!0},closeNotification(){this.showNotification=!1},async generateMonthlyExpenses(){var n,r;const e=new Date().getMonth()+1,o=new Date().getFullYear(),l=e.toString().padStart(2,"0");let g="dynamic amount";try{if(!this.isSuperAdmin){const u=await this.$axios.get("/my-building");g=`₪${parseFloat(u.data.monthly_fee).toFixed(2)}`}}catch{console.warn("Could not fetch building info for monthly fee display")}const i=this.isSuperAdmin?`Generate monthly expenses for all neighbors for ${l}/${o}?`:`Generate monthly expenses (${g}) for all neighbors in your building for ${l}/${o}?`;if(confirm(i)){this.generatingExpenses=!0;try{await this.$axios.post("/expenses/generate-monthly",{month:l,year:o.toString()}),this.showSuccess("Generated","Monthly expenses generated successfully"),this.loadExpenses()}catch(u){this.showError("Generation Failed",((r=(n=u.response)==null?void 0:n.data)==null?void 0:r.message)||"Failed to generate monthly expenses")}finally{this.generatingExpenses=!1}}}}},T={key:0},U={key:1,class:"text-gray-500 text-sm"},j={class:"bg-white p-6 rounded-lg shadow-sm mb-6"},A={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},V={class:"block text-sm font-medium text-gray-700 mb-2"},z={value:""},L={value:"building_services"},B={value:"building_electricity"},Y={value:"personal_electricity"},G={value:"water"},I={value:"other"},J={class:"block text-sm font-medium text-gray-700 mb-2"},O={value:""},R={value:"01"},q={value:"02"},H={value:"03"},K={value:"04"},Q={value:"05"},W={value:"06"},X={value:"07"},Z={value:"08"},ee={value:"09"},te={value:"10"},se={value:"11"},oe={value:"12"},ie={class:"block text-sm font-medium text-gray-700 mb-2"},ne={class:"flex items-end"},ae={class:"mt-4 flex justify-between items-center"},le=["disabled"],re={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},de={class:"bg-white p-6 rounded-lg shadow-sm"},ce={class:"text-lg font-semibold text-gray-700"},ue={class:"text-3xl font-bold text-red-600"},he={class:"bg-white p-6 rounded-lg shadow-sm"},pe={class:"text-lg font-semibold text-gray-700"},ge={class:"text-3xl font-bold text-blue-600"},me={class:"bg-white p-6 rounded-lg shadow-sm"},ye={class:"text-lg font-semibold text-gray-700"},fe={class:"text-3xl font-bold text-purple-600"},be={class:"bg-white p-6 rounded-lg shadow-sm"},xe={class:"text-lg font-semibold text-gray-700"},_e={class:"text-3xl font-bold text-orange-600"},ve={class:"flex space-x-2"},we=["onClick"],Ee=["onClick"],ke={class:"mt-6 flex justify-between items-center"},Me={class:"text-sm text-gray-700"},Se={class:"flex space-x-2"},Fe=["disabled"],$e=["disabled"],Ce={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},Pe={class:"flex items-center justify-center min-h-screen p-4"},Ne={class:"relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto"},De={class:"px-6 py-4 border-b border-gray-200"},Te={class:"text-lg font-semibold text-gray-900"},Ue={class:"p-6"};function je(e,o,l,g,i,n){const r=m("router-link"),u=m("data-table"),v=m("expense-form"),w=m("notification"),E=m("dashboard-layout");return h(),y(E,{title:e.$t("expense_management")},{sidebar:d(()=>[i.userLoaded?(h(),f("div",T,[c(r,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[p(s(e.$t("expenses")),1)]),_:1}),c(r,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[p(s(e.$t("incomes")),1)]),_:1}),c(r,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[p(s(e.$t("users")),1)]),_:1}),i.isSuperAdmin?(h(),y(r,{key:0,to:"/admin/buildings",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[p(s(e.$t("buildings")),1)]),_:1})):b("",!0),i.isSuperAdmin?b("",!0):(h(),y(r,{key:1,to:"/admin/my-building",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[p(s(e.$t("my_building")),1)]),_:1}))])):(h(),f("div",U,s(e.$t("loading_menu")),1))]),default:d(()=>[t("div",j,[t("div",A,[t("div",null,[t("label",V,s(e.$t("expense_type")),1),x(t("select",{"onUpdate:modelValue":o[0]||(o[0]=a=>i.filters.type=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md"},[t("option",z,s(e.$t("all_types")),1),t("option",L,s(e.$t("building_services")),1),t("option",B,s(e.$t("building_electricity")),1),t("option",Y,s(e.$t("personal_electricity")),1),t("option",G,s(e.$t("water")),1),t("option",I,s(e.$t("other")),1)],512),[[_,i.filters.type]])]),t("div",null,[t("label",J,s(e.$t("month")),1),x(t("select",{"onUpdate:modelValue":o[1]||(o[1]=a=>i.filters.month=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md"},[t("option",O,s(e.$t("all_months")),1),t("option",R,s(e.$t("january")),1),t("option",q,s(e.$t("february")),1),t("option",H,s(e.$t("march")),1),t("option",K,s(e.$t("april")),1),t("option",Q,s(e.$t("may")),1),t("option",W,s(e.$t("june")),1),t("option",X,s(e.$t("july")),1),t("option",Z,s(e.$t("august")),1),t("option",ee,s(e.$t("september")),1),t("option",te,s(e.$t("october")),1),t("option",se,s(e.$t("november")),1),t("option",oe,s(e.$t("december")),1)],512),[[_,i.filters.month]])]),t("div",null,[t("label",ie,s(e.$t("year")),1),x(t("input",{type:"number","onUpdate:modelValue":o[2]||(o[2]=a=>i.filters.year=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"2023",max:"2025"},null,512),[[N,i.filters.year]])]),t("div",ne,[t("button",{onClick:o[3]||(o[3]=(...a)=>n.applyFilters&&n.applyFilters(...a)),class:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"},s(e.$t("apply_filters")),1)])]),t("div",ae,[c(r,{to:"/admin/expenses/create",class:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"},{default:d(()=>[p(s(e.$t("create_expense")),1)]),_:1}),t("button",{onClick:o[4]||(o[4]=(...a)=>n.generateMonthlyExpenses&&n.generateMonthlyExpenses(...a)),disabled:i.generatingExpenses,class:"px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-gray-400"},s(i.generatingExpenses?e.$t("generating"):e.$t("generate_monthly_expenses")),9,le)])]),t("div",re,[t("div",de,[t("h3",ce,s(e.$t("total_expenses")),1),t("p",ue,"₪"+s(n.totalExpenses.toFixed(2)),1)]),t("div",he,[t("h3",pe,s(e.$t("this_month")),1),t("p",ge,"₪"+s(n.thisMonthExpenses.toFixed(2)),1)]),t("div",me,[t("h3",ye,s(e.$t("automatic")),1),t("p",fe,"₪"+s(n.automaticExpenses.toFixed(2)),1)]),t("div",be,[t("h3",xe,s(e.$t("manual")),1),t("p",_e,"₪"+s(n.manualExpenses.toFixed(2)),1)])]),c(u,{title:e.$t("expense_records"),columns:i.columns,items:i.expenses,loading:i.loading},{actions:d(({item:a})=>[t("div",ve,[t("button",{onClick:k=>n.editExpense(a),class:"text-indigo-600 hover:text-indigo-900"},s(e.$t("edit")),9,we),t("button",{onClick:k=>n.deleteExpense(a),class:"text-red-600 hover:text-red-900"},s(e.$t("delete")),9,Ee)])]),_:1},8,["title","columns","items","loading"]),t("div",ke,[t("div",Me,s(e.$t("showing_results"))+" "+s(i.pagination.from)+" "+s(e.$t("to"))+" "+s(i.pagination.to)+" "+s(e.$t("of"))+" "+s(i.pagination.total)+" "+s(e.$t("results")),1),t("div",Se,[t("button",{onClick:o[5]||(o[5]=(...a)=>n.previousPage&&n.previousPage(...a)),disabled:i.pagination.currentPage===1,class:"px-3 py-1 border rounded disabled:opacity-50"},s(e.$t("previous")),9,Fe),t("button",{onClick:o[6]||(o[6]=(...a)=>n.nextPage&&n.nextPage(...a)),disabled:i.pagination.currentPage===i.pagination.lastPage,class:"px-3 py-1 border rounded disabled:opacity-50"},s(e.$t("next")),9,$e)])]),i.showEditModal?(h(),f("div",Ce,[t("div",{class:"fixed inset-0 bg-black bg-opacity-50",onClick:o[7]||(o[7]=(...a)=>n.closeEditModal&&n.closeEditModal(...a))}),t("div",Pe,[t("div",Ne,[t("button",{onClick:o[8]||(o[8]=(...a)=>n.closeEditModal&&n.closeEditModal(...a)),class:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"},o[9]||(o[9]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),t("div",De,[t("h3",Te,s(e.$t("edit_expense")),1)]),t("div",Ue,[c(v,{expense:i.selectedExpense,"is-edit":!0,onSuccess:n.handleEditSuccess,onError:n.handleEditError,onCancel:n.closeEditModal},null,8,["expense","onSuccess","onError","onCancel"])])])])])):b("",!0),c(w,{show:i.showNotification,type:i.notificationType,title:i.notificationTitle,message:i.notificationMessage,onClose:n.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1},8,["title"])}const Ye=C(D,[["render",je]]);export{Ye as default};
