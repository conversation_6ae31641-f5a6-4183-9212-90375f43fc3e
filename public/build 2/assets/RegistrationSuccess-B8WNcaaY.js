import{_ as x,c as a,a as e,g as d,t as n,o as u}from"./app-B62tIoiF.js";const b={name:"RegistrationSuccess",data(){return{user:null}},created(){this.user=JSON.parse(localStorage.getItem("user")||"null"),this.user||this.$router.push("/login")},methods:{goToDashboard(){var o,s,l;console.log("User data:",this.user),console.log("User role:",(o=this.user)==null?void 0:o.role),((s=this.user)==null?void 0:s.role)==="admin"||((l=this.user)==null?void 0:l.role)==="super_admin"?(console.log("Redirecting to admin dashboard"),this.$router.push({name:"AdminDashboard"})):(console.log("Redirecting to neighbor dashboard"),this.$router.push({name:"NeighborDashboard"}))},async logout(){try{await this.$axios.post("/logout")}catch(o){console.error("Logout error:",o)}finally{localStorage.removeItem("token"),localStorage.removeItem("user"),this.$router.push("/login")}}}},h={class:"min-h-screen bg-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8"},v={class:"max-w-md w-full space-y-8"},y={class:"text-center"},w={class:"bg-white rounded-lg shadow-md p-6 mb-6"},_={class:"text-gray-600 mb-4"},k={class:"font-semibold text-gray-900"},j={class:"text-left space-y-2 text-sm text-gray-600"},S={class:"flex justify-between"},D={class:"flex justify-between"},B={class:"flex justify-between"},C={class:"capitalize"},N={class:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6"},R={class:"flex"},z={class:"ml-3"},M={class:"mt-2 text-sm text-blue-700"},L={class:"list-disc list-inside space-y-1"},T={key:0},I={key:1},V={class:"space-y-3"};function A(o,s,l,E,t,r){var m,c,g,f,p;return u(),a("div",h,[e("div",v,[e("div",y,[s[12]||(s[12]=e("div",{class:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)),s[13]||(s[13]=e("h2",{class:"text-3xl font-bold text-gray-900 mb-4"}," Registration Successful! ",-1)),e("div",w,[e("p",_,[s[2]||(s[2]=d(" Welcome to our community, ")),e("span",k,n((m=t.user)==null?void 0:m.name),1),s[3]||(s[3]=d("! "))]),e("div",j,[e("div",S,[s[4]||(s[4]=e("span",{class:"font-medium"},"Email:",-1)),e("span",null,n((c=t.user)==null?void 0:c.email),1)]),e("div",D,[s[5]||(s[5]=e("span",{class:"font-medium"},"Apartment:",-1)),e("span",null,n((g=t.user)==null?void 0:g.apartment_number),1)]),e("div",B,[s[6]||(s[6]=e("span",{class:"font-medium"},"Role:",-1)),e("span",C,n(((f=t.user)==null?void 0:f.role)||"Neighbor"),1)])])]),e("div",N,[e("div",R,[s[10]||(s[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z","clip-rule":"evenodd"})])],-1)),e("div",z,[s[9]||(s[9]=e("h3",{class:"text-sm font-medium text-blue-800"}," What's Next? ",-1)),e("div",M,[e("ul",L,[s[7]||(s[7]=e("li",null,"Your account has been created successfully",-1)),s[8]||(s[8]=e("li",null,"You can now access your dashboard",-1)),((p=t.user)==null?void 0:p.role)==="admin"?(u(),a("li",T,"Manage expenses and view reports")):(u(),a("li",I,"View expenses and make payments"))])])])])]),e("div",V,[e("button",{onClick:s[0]||(s[0]=(...i)=>r.goToDashboard&&r.goToDashboard(...i)),class:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"},s[11]||(s[11]=[d(" Continue to Dashboard "),e("svg",{class:"ml-2 -mr-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:s[1]||(s[1]=(...i)=>r.logout&&r.logout(...i)),class:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"}," Logout ")])])])])}const U=x(b,[["render",A]]);export{U as default};
