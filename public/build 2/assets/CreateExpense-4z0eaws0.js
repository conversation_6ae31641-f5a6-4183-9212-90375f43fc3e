import{D as p}from"./DashboardLayout-HXqH_ZDc.js";import{E as u}from"./ExpenseForm-B2fnylaP.js";import{N as m}from"./Notification-II7oLsx2.js";import{_ as x,h as _,f as t,r as a,o as g,a as l,e as s,g as c}from"./app-B62tIoiF.js";const y={components:{DashboardLayout:p,ExpenseForm:u,Notification:m},data(){return{showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:""}},methods:{handleSuccess(){this.showSuccess("Expense Created","The expense has been created successfully."),this.$router.push("/admin/expenses")},handleError(o){this.showError("Creation Failed",o)},handleCancel(){this.$router.push("/admin/expenses")},showSuccess(o,e){this.notificationType="success",this.notificationTitle=o,this.notificationMessage=e,this.showNotification=!0},showError(o,e){this.notificationType="error",this.notificationTitle=o,this.notificationMessage=e,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},b={class:"max-w-2xl mx-auto"},w={class:"bg-white p-6 rounded-lg shadow-sm"};function C(o,e,E,N,i,n){const r=a("router-link"),d=a("expense-form"),f=a("notification"),h=a("dashboard-layout");return g(),_(h,{title:"Create Expense"},{sidebar:t(()=>[s(r,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>e[0]||(e[0]=[c(" All Expenses ")])),_:1,__:[0]}),s(r,{to:"/admin/expenses/create",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>e[1]||(e[1]=[c(" Create Expense ")])),_:1,__:[1]}),s(r,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>e[2]||(e[2]=[c(" Incomes ")])),_:1,__:[2]})]),default:t(()=>[l("div",b,[l("div",w,[s(d,{onSuccess:n.handleSuccess,onError:n.handleError,onCancel:n.handleCancel},null,8,["onSuccess","onError","onCancel"])])]),s(f,{show:i.showNotification,type:i.notificationType,title:i.notificationTitle,message:i.notificationMessage,onClose:n.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1})}const M=x(y,[["render",C]]);export{M as default};
