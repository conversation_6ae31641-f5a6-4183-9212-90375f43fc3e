import{D as k}from"./DashboardLayout-HXqH_ZDc.js";import{D as A}from"./DataTable-BdcmlClz.js";import{_ as v,i as $,h as p,f as d,r as y,o as r,a as o,c as g,b,t as s,e as m,g as c}from"./app-B62tIoiF.js";const S={mixins:[$],components:{DashboardLayout:k,DataTable:A},data(){return{loading:!0,totalExpenses:0,totalIncome:0,totalUsers:0,totalBuildings:0,totalAdmins:0,totalNeighbors:0,activeBuildings:0,allExpenses:[],allIncomes:[],buildings:[],admins:[],neighborColumns:[],buildingColumns:[],adminColumns:[],isSuperAdmin:!1,userLoaded:!1}},computed:{outstandingBalance(){return this.totalExpenses-this.totalIncome},neighborSummary(){const e=new Map;return Array.isArray(this.allExpenses)&&this.allExpenses.forEach(i=>{if(i&&i.user&&i.user.id){const a=i.user.id;e.has(a)||e.set(a,{id:a,name:i.user.name||"Unknown",apartment_number:i.user.apartment_number||"N/A",total_expenses:0,total_income:0}),e.get(a).total_expenses+=parseFloat(i.amount||0)}}),Array.isArray(this.allIncomes)&&this.allIncomes.forEach(i=>{if(i&&i.user&&i.user.id){const a=i.user.id;e.has(a)||e.set(a,{id:a,name:i.user.name||"Unknown",apartment_number:i.user.apartment_number||"N/A",total_expenses:0,total_income:0}),e.get(a).total_income+=parseFloat(i.amount||0)}}),Array.from(e.values()).map(i=>({...i,outstanding_balance:i.total_expenses-i.total_income})).sort((i,a)=>{const n=i.apartment_number||"",t=a.apartment_number||"";return n.localeCompare(t)})}},async created(){this.initializeUserFromStorage(),this.initializeColumns(),await this.loadDashboardData()},methods:{initializeColumns(){this.neighborColumns=[{key:"name",label:this.$t("neighbor")},{key:"apartment_number",label:this.$t("apartment")},{key:"total_expenses",label:this.$t("total_expenses")},{key:"total_income",label:this.$t("total_income")},{key:"outstanding_balance",label:this.$t("outstanding_balance")}],this.buildingColumns=[{key:"name",label:this.$t("building_name")},{key:"city",label:this.$t("city")},{key:"monthly_fee",label:this.$t("monthly_fee")},{key:"admin_count",label:this.$t("admins")},{key:"neighbor_count",label:this.$t("neighbors")}],this.adminColumns=[{key:"name",label:this.$t("name")},{key:"email",label:this.$t("email")},{key:"building.name",label:this.$t("building")},{key:"created_at",label:this.$t("created")}]},initializeUserFromStorage(){try{const e=JSON.parse(localStorage.getItem("user")||"null");e&&e.role&&(this.isSuperAdmin=e.role==="super_admin",this.userLoaded=!0)}catch(e){console.error("Error parsing user data from localStorage:",e)}},async loadDashboardData(){this.loading=!0;try{const e=await this.$axios.get("/user");if(e.data&&(this.isSuperAdmin=e.data.role==="super_admin",this.userLoaded=!0,localStorage.setItem("user",JSON.stringify(e.data))),this.isSuperAdmin){const i=await this.$axios.get("/buildings");i.data&&Array.isArray(i.data)?(this.buildings=i.data.filter(n=>n&&n.id).map(n=>({...n,monthly_fee:`₪${parseFloat(n.monthly_fee||0).toFixed(2)}`,admin_count:0,neighbor_count:0})),this.totalBuildings=this.buildings.length,this.activeBuildings=this.buildings.filter(n=>n.admin_count>0).length):this.buildings=[];const a=await this.$axios.get("/admin/users");if(a.data.data&&Array.isArray(a.data.data)){const n=a.data.data.filter(t=>t&&t.id);this.admins=n.filter(t=>t.role==="admin"),this.totalAdmins=this.admins.length,this.totalNeighbors=n.filter(t=>t.role==="neighbor").length,this.buildings.forEach(t=>{t.admin_count=n.filter(l=>l.building_id===t.id&&l.role==="admin").length,t.neighbor_count=n.filter(l=>l.building_id===t.id&&l.role==="neighbor").length}),this.activeBuildings=this.buildings.filter(t=>t.admin_count>0).length}}else{const i=await this.$axios.get("/expenses");i.data.data&&Array.isArray(i.data.data)?(this.allExpenses=i.data.data.filter(t=>t&&t.id),this.totalExpenses=this.allExpenses.reduce((t,l)=>t+parseFloat(l.amount||0),0)):this.allExpenses=[];const a=await this.$axios.get("/incomes");a.data.data&&Array.isArray(a.data.data)?(this.allIncomes=a.data.data.filter(t=>t&&t.id),this.totalIncome=this.allIncomes.reduce((t,l)=>t+parseFloat(l.amount||0),0)):this.allIncomes=[];const n=await this.$axios.get("/admin/users");n.data.data&&(this.totalUsers=n.data.data.length)}}catch(e){console.error("Error loading dashboard data:",e)}finally{this.loading=!1}},async deleteBuilding(e){if(confirm(this.$t("confirm_delete_building",{name:e.name})))try{await this.$axios.delete(`/buildings/${e.id}`),this.buildings=this.buildings.filter(i=>i.id!==e.id),this.totalBuildings=this.buildings.length}catch(i){console.error("Error deleting building:",i),alert(this.$t("failed_delete_building"))}},editAdmin(e){this.$router.push(`/admin/users/${e.id}/edit`)},async deleteAdmin(e){if(confirm(this.$t("confirm_delete_admin",{name:e.name})))try{await this.$axios.delete(`/admin/users/${e.id}`),this.admins=this.admins.filter(i=>i.id!==e.id),this.totalAdmins=this.admins.length}catch(i){console.error("Error deleting admin:",i),alert(this.$t("failed_delete_admin"))}}}},w={key:0},C={key:1,class:"text-gray-500 text-sm"},E={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},B={class:"bg-white p-6 rounded-lg shadow-sm"},I={class:"text-lg font-semibold text-gray-700"},D={class:"text-3xl font-bold text-blue-600"},F={class:"bg-white p-6 rounded-lg shadow-sm"},N={class:"text-lg font-semibold text-gray-700"},U={class:"text-3xl font-bold text-green-600"},R={class:"bg-white p-6 rounded-lg shadow-sm"},L={class:"text-lg font-semibold text-gray-700"},z={class:"text-3xl font-bold text-purple-600"},V={class:"bg-white p-6 rounded-lg shadow-sm"},M={class:"text-lg font-semibold text-gray-700"},J={class:"text-3xl font-bold text-orange-600"},O={key:0,class:"space-y-6"},T={key:0,class:"flex space-x-2"},j=["onClick"],q={key:0,class:"flex space-x-2"},G=["onClick"],H=["onClick"],K={key:1},P={key:2,class:"flex justify-center items-center py-8"},Q={class:"text-gray-500"};function W(e,i,a,n,t,l){const h=y("router-link"),_=y("data-table"),x=y("dashboard-layout");return r(),p(x,{title:e.$t("admin_dashboard")},{sidebar:d(()=>[t.userLoaded?(r(),g("div",w,[m(h,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[c(s(e.$t("expenses")),1)]),_:1}),m(h,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[c(s(e.$t("incomes")),1)]),_:1}),m(h,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[c(s(e.$t("users")),1)]),_:1}),t.isSuperAdmin?(r(),p(h,{key:0,to:"/admin/buildings",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[c(s(e.$t("buildings")),1)]),_:1})):b("",!0),t.isSuperAdmin?b("",!0):(r(),p(h,{key:1,to:"/admin/my-building",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[c(s(e.$t("my_building")),1)]),_:1}))])):(r(),g("div",C,s(e.$t("loading_menu")),1))]),default:d(()=>[o("div",E,[o("div",B,[o("h3",I,s(t.isSuperAdmin?e.$t("total_buildings"):e.$t("total_expenses")),1),o("p",D,s(t.isSuperAdmin?t.totalBuildings:"₪"+t.totalExpenses.toFixed(2)),1)]),o("div",F,[o("h3",N,s(t.isSuperAdmin?e.$t("total_admins"):e.$t("total_income")),1),o("p",U,s(t.isSuperAdmin?t.totalAdmins:"₪"+t.totalIncome.toFixed(2)),1)]),o("div",R,[o("h3",L,s(t.isSuperAdmin?e.$t("total_neighbors"):e.$t("total_users")),1),o("p",z,s(t.isSuperAdmin?t.totalNeighbors:t.totalUsers),1)]),o("div",V,[o("h3",M,s(t.isSuperAdmin?e.$t("active_buildings"):e.$t("outstanding_balance")),1),o("p",J,s(t.isSuperAdmin?t.activeBuildings:"₪"+l.outstandingBalance.toFixed(2)),1)])]),t.isSuperAdmin&&!t.loading?(r(),g("div",O,[m(_,{title:e.$t("buildings"),columns:t.buildingColumns,items:t.buildings,loading:t.loading},{actions:d(({item:u})=>[u&&u.id?(r(),g("div",T,[m(h,{to:`/admin/buildings/${u.id}/edit`,class:"text-indigo-600 hover:text-indigo-900"},{default:d(()=>[c(s(e.$t("edit")),1)]),_:2},1032,["to"]),o("button",{onClick:f=>l.deleteBuilding(u),class:"text-red-600 hover:text-red-900"},s(e.$t("delete")),9,j)])):b("",!0)]),"header-actions":d(()=>[m(h,{to:"/admin/buildings/create",class:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"},{default:d(()=>[c(s(e.$t("add_building")),1)]),_:1})]),_:1},8,["title","columns","items","loading"]),m(_,{title:e.$t("admins"),columns:t.adminColumns,items:t.admins,loading:t.loading},{actions:d(({item:u})=>[u&&u.id?(r(),g("div",q,[o("button",{onClick:f=>l.editAdmin(u),class:"text-indigo-600 hover:text-indigo-900"},s(e.$t("edit")),9,G),o("button",{onClick:f=>l.deleteAdmin(u),class:"text-red-600 hover:text-red-900"},s(e.$t("delete")),9,H)])):b("",!0)]),"header-actions":d(()=>[m(h,{to:"/admin/users/create",class:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"},{default:d(()=>[c(s(e.$t("add_admin")),1)]),_:1})]),_:1},8,["title","columns","items","loading"])])):t.loading?b("",!0):(r(),g("div",K,[m(_,{title:e.$t("neighbor_financial_summary"),columns:t.neighborColumns,items:l.neighborSummary,loading:t.loading},null,8,["title","columns","items","loading"])])),t.loading?(r(),g("div",P,[o("div",Q,s(e.$t("loading_dashboard_data")),1)])):b("",!0)]),_:1},8,["title"])}const tt=v(S,[["render",W]]);export{tt as default};
