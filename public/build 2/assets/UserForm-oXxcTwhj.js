import{_ as c,i as g,c as d,b as u,a as o,t as n,w as a,v as m,j as p,F as y,k as _,d as h,o as l}from"./app-B62tIoiF.js";const w={name:"UserForm",mixins:[g],props:{user:{type:Object,default:()=>({name:"",email:"",apartment_number:"",role:"",building_id:null,password:"",password_confirmation:""})},isEdit:{type:Boolean,default:!1},isSuperAdmin:{type:Boolean,default:!1},adminBuildingId:{type:[Number,String],default:null},buildings:{type:Array,default:()=>[]}},data(){return{processing:!1,formData:{name:"",email:"",apartment_number:"",role:"",building_id:null,password:"",password_confirmation:""}}},watch:{user:{handler(e){e&&this.isEdit&&(this.formData={name:e.name||"",email:e.email||"",apartment_number:e.apartment_number||"",role:e.role||"",building_id:e.building_id||null,password:"",password_confirmation:""})},immediate:!0,deep:!0}},mounted(){this.isEdit&&this.user&&(this.formData={name:this.user.name||"",email:this.user.email||"",apartment_number:this.user.apartment_number||"",role:this.user.role||"",building_id:this.user.building_id||null,password:"",password_confirmation:""})},methods:{async handleSubmit(){var e,s;this.processing=!0;try{const i={...this.formData};this.isEdit&&!i.password&&(delete i.password,delete i.password_confirmation),i.role==="super_admin"?i.building_id=null:!this.isSuperAdmin&&this.adminBuildingId&&(i.building_id=this.adminBuildingId);const b=await this.$axios[this.isEdit?"put":"post"](this.isEdit?`/admin/users/${this.user.id}`:"/admin/users",i);this.$emit("success",b.data)}catch(i){this.$emit("error",((s=(e=i.response)==null?void 0:e.data)==null?void 0:s.message)||"Failed to save user")}finally{this.processing=!1}}}},v={key:0,class:"text-xl font-semibold text-gray-900 mb-6"},x={for:"name",class:"block text-sm font-medium text-gray-700 mb-2"},D={for:"email",class:"block text-sm font-medium text-gray-700 mb-2"},k={for:"apartment_number",class:"block text-sm font-medium text-gray-700 mb-2"},S={for:"role",class:"block text-sm font-medium text-gray-700 mb-2"},E={value:""},V={key:0,value:"super_admin"},q={key:1,value:"admin"},B={key:2,value:"neighbor"},A={key:0},F={for:"building_id",class:"block text-sm font-medium text-gray-700 mb-2"},U={value:""},M=["value"],j={for:"password",class:"block text-sm font-medium text-gray-700 mb-2"},I=["required"],N={for:"password_confirmation",class:"block text-sm font-medium text-gray-700 mb-2"},C=["required"],L={class:"flex justify-end space-x-3 pt-4"},O=["disabled"];function T(e,s,i,b,t,f){return l(),d("div",null,[i.isEdit?u("",!0):(l(),d("h2",v,n(e.$t("create_user")),1)),o("form",{onSubmit:s[8]||(s[8]=h((...r)=>f.handleSubmit&&f.handleSubmit(...r),["prevent"])),class:"space-y-6"},[o("div",null,[o("label",x,n(e.$t("name")),1),a(o("input",{type:"text",id:"name","onUpdate:modelValue":s[0]||(s[0]=r=>t.formData.name=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},null,512),[[m,t.formData.name]])]),o("div",null,[o("label",D,n(e.$t("email")),1),a(o("input",{type:"email",id:"email","onUpdate:modelValue":s[1]||(s[1]=r=>t.formData.email=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},null,512),[[m,t.formData.email]])]),o("div",null,[o("label",k,n(e.$t("apartment_number")),1),a(o("input",{type:"text",id:"apartment_number","onUpdate:modelValue":s[2]||(s[2]=r=>t.formData.apartment_number=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},null,512),[[m,t.formData.apartment_number]])]),o("div",null,[o("label",S,n(e.$t("role")),1),a(o("select",{id:"role","onUpdate:modelValue":s[3]||(s[3]=r=>t.formData.role=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},[o("option",E,n(e.$t("role")),1),i.isSuperAdmin?(l(),d("option",V,n(e.$t("super_admin")),1)):u("",!0),i.isSuperAdmin?(l(),d("option",q,n(e.$t("admin")),1)):u("",!0),i.isSuperAdmin?u("",!0):(l(),d("option",B,n(e.$t("neighbor")),1))],512),[[p,t.formData.role]])]),i.isSuperAdmin&&t.formData.role&&t.formData.role!=="super_admin"?(l(),d("div",A,[o("label",F,n(e.$t("building")),1),a(o("select",{id:"building_id","onUpdate:modelValue":s[4]||(s[4]=r=>t.formData.building_id=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[o("option",U,n(e.$t("building")),1),(l(!0),d(y,null,_(i.buildings,r=>(l(),d("option",{key:r.id,value:r.id},n(r.name),9,M))),128))],512),[[p,t.formData.building_id]])])):u("",!0),o("div",null,[o("label",j,n(e.$t("password"))+" "+n(i.isEdit?"(leave blank to keep current)":""),1),a(o("input",{type:"password",id:"password","onUpdate:modelValue":s[5]||(s[5]=r=>t.formData.password=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!i.isEdit},null,8,I),[[m,t.formData.password]])]),o("div",null,[o("label",N,n(e.$t("confirm_password")),1),a(o("input",{type:"password",id:"password_confirmation","onUpdate:modelValue":s[6]||(s[6]=r=>t.formData.password_confirmation=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!i.isEdit},null,8,C),[[m,t.formData.password_confirmation]])]),o("div",L,[o("button",{type:"button",onClick:s[7]||(s[7]=r=>e.$emit("cancel")),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"},n(e.$t("cancel")),1),o("button",{type:"submit",disabled:t.processing,class:"px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"},n(t.processing?e.$t("loading"):i.isEdit?e.$t("edit_user"):e.$t("create_user")),9,O)])],32)])}const G=c(w,[["render",T]]);export{G as U};
