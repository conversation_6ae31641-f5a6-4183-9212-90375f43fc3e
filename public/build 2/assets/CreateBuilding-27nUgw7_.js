import{D as _}from"./DashboardLayout-HXqH_ZDc.js";import{B as b}from"./BuildingForm-BCC_xiW2.js";import{N as x}from"./Notification-II7oLsx2.js";import{_ as S,i as v,h as u,f as t,r as d,o as n,a as g,e as a,c as h,b as m,g as r,t as o}from"./app-B62tIoiF.js";const w={mixins:[v],components:{DashboardLayout:_,BuildingForm:b,Notification:x},mounted(){this.initializeUserFromStorage()},data(){return{showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:"",userLoaded:!1,isSuperAdmin:!1}},methods:{initializeUserFromStorage(){try{const e=JSON.parse(localStorage.getItem("user")||"null");e&&e.role&&(this.isSuperAdmin=e.role==="super_admin",this.userLoaded=!0)}catch(e){console.error("Error parsing user data from localStorage:",e)}},handleSuccess(e){this.showSuccess(this.$t("success"),this.$t("building_created")),setTimeout(()=>{this.$router.push("/admin/buildings")},1500)},handleError(e){this.showError(this.$t("error"),e)},handleCancel(){this.$router.push("/admin/buildings")},showSuccess(e,c){this.notificationType="success",this.notificationTitle=e,this.notificationMessage=c,this.showNotification=!0},showError(e,c){this.notificationType="error",this.notificationTitle=e,this.notificationMessage=c,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},k={key:0},N={key:1,class:"text-gray-500 text-sm"},C={class:"max-w-2xl mx-auto"},T={class:"bg-white p-6 rounded-lg shadow-sm"};function E(e,c,B,M,i,l){const s=d("router-link"),f=d("building-form"),p=d("notification"),y=d("dashboard-layout");return n(),u(y,{title:e.$t("create_building")},{sidebar:t(()=>[i.userLoaded?(n(),h("div",k,[a(s,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>[r(o(e.$t("expenses")),1)]),_:1}),a(s,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>[r(o(e.$t("incomes")),1)]),_:1}),a(s,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>[r(o(e.$t("users")),1)]),_:1}),i.isSuperAdmin?(n(),u(s,{key:0,to:"/admin/buildings",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>[r(o(e.$t("buildings")),1)]),_:1})):m("",!0),i.isSuperAdmin?m("",!0):(n(),u(s,{key:1,to:"/admin/my-building",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:t(()=>[r(o(e.$t("my_building")),1)]),_:1}))])):(n(),h("div",N,o(e.$t("loading_menu")),1))]),default:t(()=>[g("div",C,[g("div",T,[a(f,{onSuccess:l.handleSuccess,onError:l.handleError,onCancel:l.handleCancel},null,8,["onSuccess","onError","onCancel"])])]),a(p,{show:i.showNotification,type:i.notificationType,title:i.notificationTitle,message:i.notificationMessage,onClose:l.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1},8,["title"])}const V=S(w,[["render",E]]);export{V as default};
