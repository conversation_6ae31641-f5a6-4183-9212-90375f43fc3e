import{_ as x,i as m,c as n,a as o,b as u,m as f,t as l,F as y,k as g,o as a,n as _}from"./app-B62tIoiF.js";const k={name:"DataTable",mixins:[m],props:{title:{type:String,required:!0},columns:{type:Array,required:!0},items:{type:Array,required:!0}},computed:{filteredItems(){return this.items.filter(s=>s!=null)}},methods:{getNestedValue(s,e){if(!s||!e)return"";if(e==="is_automatic")return s[e]?this.$t("yes"):this.$t("no");if(e==="amount"){const t=s[e];return t?`₪${parseFloat(t).toFixed(2)}`:"₪0.00"}if(e==="month")return{"01":this.$t("january"),"02":this.$t("february"),"03":this.$t("march"),"04":this.$t("april"),"05":this.$t("may"),"06":this.$t("june"),"07":this.$t("july"),"08":this.$t("august"),"09":this.$t("september"),10:this.$t("october"),11:this.$t("november"),12:this.$t("december")}[s[e]]||s[e];if(e==="payment_method")return{cash:this.$t("cash"),bank_transfer:this.$t("bank_transfer"),check:this.$t("check")}[s[e]]||s[e];if(e==="role"){const t=s[e];return t?this.$t(t)||t.charAt(0).toUpperCase()+t.slice(1):""}if(e==="total_expenses"||e==="total_income"||e==="outstanding_balance"){const t=s[e];return t!==void 0?`₪${parseFloat(t).toFixed(2)}`:"₪0.00"}if(e==="payment_date"||e==="created_at"){const t=s[e];if(t){const c=this.$locale()==="ar"?"ar-SA":"en-US";return new Date(t).toLocaleDateString(c)}return""}return e.split(".").reduce((t,c)=>t&&t[c]!==void 0?t[c]:"",s)},getOutstandingBalanceClass(s){const e=parseFloat(s);return e===0?"bg-green-100 text-green-800":e>200?"bg-red-100 text-red-800":e>0?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}}},p={class:"mb-4 flex justify-between items-center"},$={class:"text-lg font-semibold"},b={class:"overflow-x-auto"},w={class:"min-w-full divide-y divide-gray-200"},v={class:"bg-gray-50"},N={key:0,class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},V={class:"bg-white divide-y divide-gray-200"},F={key:2,class:"text-red-600 font-medium"},D={key:3,class:"text-green-600 font-medium"},S={key:4},B={key:0,class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},C={key:0,class:"text-center py-4 text-gray-500"};function A(s,e,t,c,q,d){return a(),n("div",null,[o("div",p,[o("h3",$,l(t.title),1),f(s.$slots,"header-actions")]),o("div",b,[o("table",w,[o("thead",v,[o("tr",null,[(a(!0),n(y,null,g(t.columns,r=>(a(),n("th",{key:r.key,class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},l(r.label),1))),128)),s.$slots.actions?(a(),n("th",N,l(s.$t("actions")),1)):u("",!0)])]),o("tbody",V,[(a(!0),n(y,null,g(d.filteredItems,(r,h)=>(a(),n("tr",{key:(r==null?void 0:r.id)||h},[(a(!0),n(y,null,g(t.columns,i=>(a(),n("td",{key:i.key,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},[i.key==="role"?(a(),n("span",{key:0,class:_(["px-2 inline-flex text-xs leading-5 font-semibold rounded-full",(r==null?void 0:r.role)==="admin"?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"])},l(d.getNestedValue(r,i.key)),3)):i.key==="outstanding_balance"?(a(),n("span",{key:1,class:_(["font-semibold px-2 py-1 rounded",d.getOutstandingBalanceClass(r==null?void 0:r.outstanding_balance)])},l(d.getNestedValue(r,i.key)),3)):i.key==="total_expenses"?(a(),n("span",F,l(d.getNestedValue(r,i.key)),1)):i.key==="total_income"?(a(),n("span",D,l(d.getNestedValue(r,i.key)),1)):(a(),n("span",S,l(d.getNestedValue(r,i.key)),1))]))),128)),s.$slots.actions?(a(),n("td",B,[f(s.$slots,"actions",{item:r})])):u("",!0)]))),128))])])]),d.filteredItems.length===0?(a(),n("div",C,l(s.$t("no_data")),1)):u("",!0)])}const L=x(k,[["render",A]]);export{L as D};
