import{D as S}from"./DashboardLayout-HXqH_ZDc.js";import{D as U}from"./DataTable-BdcmlClz.js";import{U as A}from"./UserForm-oXxcTwhj.js";import{N as M}from"./Notification-II7oLsx2.js";import{_ as N,i as B,h as p,f as u,r as f,o,c as n,b as a,e as g,a as i,w as y,j as x,F as C,k as F,t as d,g as h}from"./app-B62tIoiF.js";const T={mixins:[B],components:{DashboardLayout:S,DataTable:U,UserForm:A,Notification:M},name:"UserManagement",data(){return{loading:!1,users:[],buildings:[],selectedUser:null,showEditModal:!1,showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:"",filters:{role:"",building_id:""},user:null,isAdmin:!1,isSuperAdmin:!1,adminBuildingId:null,userLoaded:!1,columns:[]}},computed:{filteredUsers(){let e=this.users;return this.filters.role&&(e=e.filter(s=>s.role===this.filters.role)),this.filters.building_id&&(e=e.filter(s=>s.building_id==this.filters.building_id)),e},totalUsers(){return this.users.length},roleBasedCount(){return this.isSuperAdmin?this.users.filter(e=>e.role==="admin").length:this.users.filter(e=>e.role==="neighbor").length},thirdMetric(){return this.isSuperAdmin?this.buildings.length:this.users.filter(e=>e.role!=="super_admin").length}},async mounted(){this.initializeUserFromStorage(),await this.fetchUser(),this.isAdmin&&(this.setupColumns(),this.loadUsers(),this.fetchBuildings())},methods:{initializeUserFromStorage(){try{const e=JSON.parse(localStorage.getItem("user")||"null");e&&e.role&&(this.isAdmin=e.role==="admin"||e.role==="super_admin",this.isSuperAdmin=e.role==="super_admin",this.adminBuildingId=e.building_id,this.userLoaded=!0)}catch(e){console.error("Error parsing user data from localStorage:",e)}},async fetchUser(){try{const e=await this.$axios.get("/user");this.user=e.data,this.isAdmin=this.user.role==="admin"||this.user.role==="super_admin",this.isSuperAdmin=this.user.role==="super_admin",this.adminBuildingId=this.user.building_id,this.userLoaded=!0,localStorage.setItem("user",JSON.stringify(this.user))}catch(e){console.error("Error fetching user:",e),this.$router.push("/login")}finally{this.loading=!1}},setupColumns(){this.isSuperAdmin?this.columns=[{key:"name",label:"Name"},{key:"email",label:"Email"},{key:"role",label:"Role"},{key:"building.name",label:"Building"}]:this.columns=[{key:"name",label:"Name"},{key:"email",label:"Email"},{key:"apartment_number",label:"Apartment"},{key:"role",label:"Role"}]},async loadUsers(){this.loading=!0;try{const e=await this.$axios.get("/admin/users");let s=e.data.data||e.data;this.isSuperAdmin?this.users=s.filter(c=>c.role==="admin"):this.users=s.filter(c=>c.building_id===this.adminBuildingId&&c.role==="neighbor")}catch(e){console.error("Error fetching users:",e),this.showError("Error","Failed to load users")}finally{this.loading=!1}},async fetchBuildings(){try{const e=await this.$axios.get("/buildings");this.buildings=e.data}catch(e){console.error("Error fetching buildings:",e)}},applyFilters(){},editUser(e){this.selectedUser=e,this.showEditModal=!0},closeEditModal(){this.showEditModal=!1,this.selectedUser=null},handleEditSuccess(e){this.showSuccess("Success","User updated successfully"),this.closeEditModal(),this.loadUsers()},handleEditError(e){this.showError("Error",e)},async deleteUser(e){var s,c;if(confirm(`Are you sure you want to delete user "${e.name}"?`))try{await this.$axios.delete(`/admin/users/${e.id}`),this.showSuccess("Success","User deleted successfully"),this.loadUsers()}catch(b){this.showError("Error",((c=(s=b.response)==null?void 0:s.data)==null?void 0:c.message)||"Failed to delete user")}},showSuccess(e,s){this.notificationType="success",this.notificationTitle=e,this.notificationMessage=s,this.showNotification=!0},showError(e,s){this.notificationType="error",this.notificationTitle=e,this.notificationMessage=s,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},D={key:0},L={key:1,class:"text-gray-500 text-sm"},I={key:0,class:"text-center py-4"},V={key:1},j={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},R={key:0,value:"admin"},z={key:1,value:"neighbor"},J={key:0},O=["value"],Y={class:"flex items-end"},q={class:"mt-4 flex justify-between items-center"},G={key:2,class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"},H={class:"bg-white p-6 rounded-lg shadow-sm"},K={class:"text-3xl font-bold text-blue-600"},P={class:"bg-white p-6 rounded-lg shadow-sm"},Q={class:"text-lg font-semibold text-gray-700"},W={class:"text-3xl font-bold text-green-600"},X={class:"bg-white p-6 rounded-lg shadow-sm"},Z={class:"text-lg font-semibold text-gray-700"},$={class:"text-3xl font-bold text-purple-600"},ee={class:"flex space-x-2"},se=["onClick"],ie=["onClick"],te={key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center"},oe={class:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4"},re={class:"flex justify-between items-center p-6 border-b"},le={class:"p-6"},ne={key:5,class:"text-center py-8"};function de(e,s,c,b,t,r){const m=f("router-link"),_=f("data-table"),v=f("user-form"),w=f("notification"),k=f("dashboard-layout");return o(),p(k,{title:e.$t("user_management")},{sidebar:u(()=>[t.userLoaded?(o(),n("div",D,[g(m,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:u(()=>[h(d(e.$t("expenses")),1)]),_:1}),g(m,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:u(()=>[h(d(e.$t("incomes")),1)]),_:1}),g(m,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:u(()=>[h(d(e.$t("users")),1)]),_:1}),t.isSuperAdmin?(o(),p(m,{key:0,to:"/admin/buildings",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:u(()=>[h(d(e.$t("buildings")),1)]),_:1})):a("",!0),t.isSuperAdmin?a("",!0):(o(),p(m,{key:1,to:"/admin/my-building",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:u(()=>[h(d(e.$t("my_building")),1)]),_:1}))])):(o(),n("div",L," Loading menu... "))]),default:u(()=>[t.loading?(o(),n("div",I,s[4]||(s[4]=[i("p",null,"Loading...",-1)]))):t.isAdmin?(o(),n("div",V,[i("div",j,[i("div",null,[s[6]||(s[6]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Role",-1)),y(i("select",{"onUpdate:modelValue":s[0]||(s[0]=l=>t.filters.role=l),class:"w-full px-3 py-2 border border-gray-300 rounded-md"},[s[5]||(s[5]=i("option",{value:""},"All Roles",-1)),t.isSuperAdmin?(o(),n("option",R,"Admin")):a("",!0),t.isSuperAdmin?a("",!0):(o(),n("option",z,"Neighbor"))],512),[[x,t.filters.role]])]),t.isSuperAdmin?(o(),n("div",J,[s[8]||(s[8]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Building",-1)),y(i("select",{"onUpdate:modelValue":s[1]||(s[1]=l=>t.filters.building_id=l),class:"w-full px-3 py-2 border border-gray-300 rounded-md"},[s[7]||(s[7]=i("option",{value:""},"All Buildings",-1)),(o(!0),n(C,null,F(t.buildings,l=>(o(),n("option",{key:l.id,value:l.id},d(l.name),9,O))),128))],512),[[x,t.filters.building_id]])])):a("",!0),i("div",Y,[i("button",{onClick:s[2]||(s[2]=(...l)=>r.applyFilters&&r.applyFilters(...l)),class:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"}," Apply Filters ")])]),i("div",q,[g(m,{to:"/admin/users/create",class:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"},{default:u(()=>s[9]||(s[9]=[h(" Add New User ")])),_:1,__:[9]})])])):a("",!0),t.isAdmin?(o(),n("div",G,[i("div",H,[s[10]||(s[10]=i("h3",{class:"text-lg font-semibold text-gray-700"},"Total Users",-1)),i("p",K,d(r.totalUsers),1)]),i("div",P,[i("h3",Q,d(t.isSuperAdmin?"Admins":"Neighbors"),1),i("p",W,d(r.roleBasedCount),1)]),i("div",X,[i("h3",Z,d(t.isSuperAdmin?"Buildings":"Active Users"),1),i("p",$,d(r.thirdMetric),1)])])):a("",!0),t.isAdmin?(o(),p(_,{key:3,title:"Users",columns:t.columns,items:r.filteredUsers,loading:t.loading},{actions:u(({item:l})=>[i("div",ee,[i("button",{onClick:E=>r.editUser(l),class:"text-indigo-600 hover:text-indigo-900"}," Edit ",8,se),i("button",{onClick:E=>r.deleteUser(l),class:"text-red-600 hover:text-red-900"}," Delete ",8,ie)])]),_:1},8,["columns","items","loading"])):a("",!0),t.showEditModal?(o(),n("div",te,[i("div",oe,[i("div",re,[s[12]||(s[12]=i("h2",{class:"text-xl font-semibold text-gray-900"},"Edit User",-1)),i("button",{onClick:s[3]||(s[3]=(...l)=>r.closeEditModal&&r.closeEditModal(...l)),class:"text-gray-400 hover:text-gray-600"},s[11]||(s[11]=[i("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("div",le,[g(v,{user:t.selectedUser,"is-edit":!0,"is-super-admin":t.isSuperAdmin,"admin-building-id":t.adminBuildingId,buildings:t.buildings,onSuccess:r.handleEditSuccess,onError:r.handleEditError,onCancel:r.closeEditModal},null,8,["user","is-super-admin","admin-building-id","buildings","onSuccess","onError","onCancel"])])])])):a("",!0),g(w,{show:t.showNotification,type:t.notificationType,title:t.notificationTitle,message:t.notificationMessage,onClose:r.closeNotification},null,8,["show","type","title","message","onClose"]),t.isAdmin?a("",!0):(o(),n("div",ne,s[13]||(s[13]=[i("p",{class:"text-red-500"},"You do not have permission to access this page.",-1)])))]),_:1},8,["title"])}const he=N(T,[["render",de]]);export{he as default};
