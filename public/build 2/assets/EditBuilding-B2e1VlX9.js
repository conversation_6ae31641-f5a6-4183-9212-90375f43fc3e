import{D as y}from"./DashboardLayout-HXqH_ZDc.js";import{B as _}from"./BuildingForm-BCC_xiW2.js";import{N as x}from"./Notification-II7oLsx2.js";import{_ as w,i as k,h as g,f as s,r as u,o as e,c as r,e as l,a as h,t as o,b as m,g as d}from"./app-B62tIoiF.js";const v={mixins:[k],components:{DashboardLayout:y,BuildingForm:_,Notification:x},data(){return{loading:!1,building:null,showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:"",userLoaded:!1,isSuperAdmin:!1}},async mounted(){this.initializeUserFromStorage(),await this.loadBuilding()},methods:{initializeUserFromStorage(){try{const i=JSON.parse(localStorage.getItem("user")||"null");i&&i.role&&(this.isSuperAdmin=i.role==="super_admin",this.userLoaded=!0)}catch(i){console.error("Error parsing user data from localStorage:",i)}},async loadBuilding(){this.loading=!0;try{const i=this.$route.params.id,n=await this.$axios.get(`/buildings/${i}`);this.building=n.data}catch(i){console.error("Error loading building:",i),this.showError(this.$t("error"),this.$t("failed_load_building"))}finally{this.loading=!1}},handleSuccess(i){this.showSuccess(this.$t("success"),this.$t("building_updated")),setTimeout(()=>{this.$router.push("/admin/buildings")},1500)},handleError(i){this.showError(this.$t("error"),i)},handleCancel(){this.$router.push("/admin/buildings")},showSuccess(i,n){this.notificationType="success",this.notificationTitle=i,this.notificationMessage=n,this.showNotification=!0},showError(i,n){this.notificationType="error",this.notificationTitle=i,this.notificationMessage=n,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},S={key:0},N={key:1,class:"text-gray-500 text-sm"},E={key:0,class:"text-center py-4"},T={key:1,class:"max-w-2xl mx-auto"},B={class:"bg-white p-6 rounded-lg shadow-sm"},C={key:2,class:"text-center py-8"},$={class:"text-red-500"};function M(i,n,A,D,t,c){const a=u("router-link"),f=u("building-form"),p=u("notification"),b=u("dashboard-layout");return e(),g(b,{title:i.$t("edit_building")},{sidebar:s(()=>[t.userLoaded?(e(),r("div",S,[l(a,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:s(()=>[d(o(i.$t("expenses")),1)]),_:1}),l(a,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:s(()=>[d(o(i.$t("incomes")),1)]),_:1}),l(a,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:s(()=>[d(o(i.$t("users")),1)]),_:1}),t.isSuperAdmin?(e(),g(a,{key:0,to:"/admin/buildings",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:s(()=>[d(o(i.$t("buildings")),1)]),_:1})):m("",!0),t.isSuperAdmin?m("",!0):(e(),g(a,{key:1,to:"/admin/my-building",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:s(()=>[d(o(i.$t("my_building")),1)]),_:1}))])):(e(),r("div",N,o(i.$t("loading_menu")),1))]),default:s(()=>[t.loading?(e(),r("div",E,[h("p",null,o(i.$t("loading_building")),1)])):t.building?(e(),r("div",T,[h("div",B,[l(f,{building:t.building,"is-edit":!0,"is-my-building":!1,onSuccess:c.handleSuccess,onError:c.handleError,onCancel:c.handleCancel},null,8,["building","onSuccess","onError","onCancel"])])])):(e(),r("div",C,[h("p",$,o(i.$t("building_not_found")),1)])),l(p,{show:t.showNotification,type:t.notificationType,title:t.notificationTitle,message:t.notificationMessage,onClose:c.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1},8,["title"])}const I=w(v,[["render",M]]);export{I as default};
