import{_ as h,i as y,c as l,b as g,a as t,t as o,w as a,j as d,F as f,k as b,v as m,l as _,d as x,o as u}from"./app-B62tIoiF.js";const v={name:"ExpenseForm",mixins:[y],props:{expense:{type:Object,default:()=>({expense_type_id:"",user_id:"",month:"",year:new Date().getFullYear(),amount:"",notes:"",is_automatic:!1})},isEdit:{type:Boolean,default:!1}},data(){return{processing:!1,expenseTypes:[],neighbors:[],formData:{expense_type_id:"",user_id:"",month:"",year:new Date().getFullYear(),amount:"",notes:"",is_automatic:!1}}},watch:{expense:{handler(e){e&&this.isEdit&&(this.formData={expense_type_id:e.expense_type_id||"",user_id:e.user_id||"",month:e.month||"",year:e.year||new Date().getFullYear(),amount:e.amount||"",notes:e.notes||"",is_automatic:e.is_automatic||!1})},immediate:!0,deep:!0}},created(){this.fetchExpenseTypes(),this.fetchNeighbors()},mounted(){this.isEdit&&this.expense&&(this.formData={expense_type_id:this.expense.expense_type_id||"",user_id:this.expense.user_id||"",month:this.expense.month||"",year:this.expense.year||new Date().getFullYear(),amount:this.expense.amount||"",notes:this.expense.notes||"",is_automatic:this.expense.is_automatic||!1})},methods:{async fetchExpenseTypes(){try{const e=await this.$axios.get("/expense-types");this.expenseTypes=e.data}catch(e){console.error("Error fetching expense types:",e)}},async fetchNeighbors(){try{const e=await this.$axios.get("/admin/users"),s=e.data.data||e.data;this.neighbors=s.filter(i=>i.role==="neighbor")}catch(e){console.error("Error fetching neighbors:",e)}},async handleSubmit(){var e,s;this.processing=!0;try{const i={...this.formData,month:this.formData.month.toString().padStart(2,"0"),year:parseInt(this.formData.year)},p=await this.$axios[this.isEdit?"put":"post"](this.isEdit?`/expenses/${this.expense.id}`:"/expenses",i);this.$emit("success",p.data)}catch(i){this.$emit("error",((s=(e=i.response)==null?void 0:e.data)==null?void 0:s.message)||"Failed to save expense")}finally{this.processing=!1}}}},D={key:0,class:"text-xl font-semibold text-gray-900 mb-6"},w={for:"expense_type",class:"block text-sm font-medium text-gray-700 mb-2"},$={value:""},k=["value"],F={for:"user",class:"block text-sm font-medium text-gray-700 mb-2"},V={value:""},E=["value"],S={for:"month",class:"block text-sm font-medium text-gray-700 mb-2"},U={value:""},j={value:"01"},T={value:"02"},q={value:"03"},M={value:"04"},B={value:"05"},N={value:"06"},Y={value:"07"},C={value:"08"},z={value:"09"},I={value:"10"},L={value:"11"},O={value:"12"},A={for:"year",class:"block text-sm font-medium text-gray-700 mb-2"},G={for:"amount",class:"block text-sm font-medium text-gray-700 mb-2"},H={class:"relative"},J={for:"notes",class:"block text-sm font-medium text-gray-700 mb-2"},K=["placeholder"],P={class:"flex items-center"},Q={for:"is_automatic",class:"ml-2 text-sm text-gray-700"},R={class:"flex justify-end space-x-3 pt-4"},W=["disabled"];function X(e,s,i,p,r,c){return u(),l("div",null,[i.isEdit?g("",!0):(u(),l("h2",D,o(e.$t("create_expense")),1)),t("form",{onSubmit:s[8]||(s[8]=x((...n)=>c.handleSubmit&&c.handleSubmit(...n),["prevent"])),class:"space-y-6"},[t("div",null,[t("label",w,o(e.$t("expense_type")),1),a(t("select",{id:"expense_type","onUpdate:modelValue":s[0]||(s[0]=n=>r.formData.expense_type_id=n),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},[t("option",$,o(e.$t("expense_type")),1),(u(!0),l(f,null,b(r.expenseTypes,n=>(u(),l("option",{key:n.id,value:n.id},o(n.name),9,k))),128))],512),[[d,r.formData.expense_type_id]])]),t("div",null,[t("label",F,o(e.$t("neighbor")),1),a(t("select",{id:"user","onUpdate:modelValue":s[1]||(s[1]=n=>r.formData.user_id=n),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},[t("option",V,o(e.$t("neighbor")),1),(u(!0),l(f,null,b(r.neighbors,n=>(u(),l("option",{key:n.id,value:n.id},o(n.name)+" ("+o(e.$t("apartment_number"))+": "+o(n.apartment_number)+") ",9,E))),128))],512),[[d,r.formData.user_id]])]),t("div",null,[t("label",S,o(e.$t("month")),1),a(t("select",{id:"month","onUpdate:modelValue":s[2]||(s[2]=n=>r.formData.month=n),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},[t("option",U,o(e.$t("month")),1),t("option",j,o(e.$t("january")),1),t("option",T,o(e.$t("february")),1),t("option",q,o(e.$t("march")),1),t("option",M,o(e.$t("april")),1),t("option",B,o(e.$t("may")),1),t("option",N,o(e.$t("june")),1),t("option",Y,o(e.$t("july")),1),t("option",C,o(e.$t("august")),1),t("option",z,o(e.$t("september")),1),t("option",I,o(e.$t("october")),1),t("option",L,o(e.$t("november")),1),t("option",O,o(e.$t("december")),1)],512),[[d,r.formData.month]])]),t("div",null,[t("label",A,o(e.$t("year")),1),a(t("input",{type:"number",id:"year","onUpdate:modelValue":s[3]||(s[3]=n=>r.formData.year=n),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",min:"2023",max:"2030",required:""},null,512),[[m,r.formData.year]])]),t("div",null,[t("label",G,o(e.$t("amount")),1),t("div",H,[s[9]||(s[9]=t("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[t("span",{class:"text-gray-500 text-sm"},"₪")],-1)),a(t("input",{type:"number",id:"amount","onUpdate:modelValue":s[4]||(s[4]=n=>r.formData.amount=n),class:"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00",step:"0.01",min:"0",required:""},null,512),[[m,r.formData.amount]])])]),t("div",null,[t("label",J,o(e.$t("notes")),1),a(t("textarea",{id:"notes","onUpdate:modelValue":s[5]||(s[5]=n=>r.formData.notes=n),rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none",placeholder:e.$t("notes")},null,8,K),[[m,r.formData.notes]])]),t("div",P,[a(t("input",{type:"checkbox",id:"is_automatic","onUpdate:modelValue":s[6]||(s[6]=n=>r.formData.is_automatic=n),class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"},null,512),[[_,r.formData.is_automatic]]),t("label",Q,o(e.$t("automatic")),1)]),t("div",R,[t("button",{type:"button",onClick:s[7]||(s[7]=n=>e.$emit("cancel")),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"},o(e.$t("cancel")),1),t("button",{type:"submit",disabled:r.processing,class:"px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"},o(r.processing?e.$t("loading"):i.isEdit?e.$t("edit_expense"):e.$t("create_expense")),9,W)])],32)])}const ee=h(v,[["render",X]]);export{ee as E};
