import{_ as c,i as b,c as m,a as e,t as n,b as _,w as a,v as l,e as w,f,r as h,d as g,o as p,g as y}from"./app-B62tIoiF.js";const x={mixins:[b],data(){return{name:"",email:"",apartment_number:"",password:"",password_confirmation:"",error:""}},methods:{async register(){var t,s;this.error="";try{const i=await this.$axios.post("/register",{name:this.name,email:this.email,apartment_number:this.apartment_number,password:this.password,password_confirmation:this.password_confirmation});localStorage.setItem("token",i.data.token),localStorage.setItem("user",JSON.stringify(i.data.user)),this.$router.push("/registration-success")}catch(i){this.error=((s=(t=i.response)==null?void 0:t.data)==null?void 0:s.message)||"Registration failed."}}}},v={class:"max-w-md mx-auto bg-white p-8 rounded shadow"},k={class:"text-2xl font-bold mb-6 text-center"},V={class:"mb-4"},q={class:"block mb-1"},N={class:"mb-4"},S={class:"block mb-1"},U={class:"mb-4"},B={class:"block mb-1"},C={class:"mb-4"},M={class:"block mb-1"},D={class:"mb-4"},I={class:"block mb-1"},R={key:0,class:"text-red-600 mb-4"},T={type:"submit",class:"w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700"},E={class:"mt-4 text-center"};function J(t,s,i,O,o,d){const u=h("router-link");return p(),m("div",v,[e("h2",k,n(t.$t("register"))+" - "+n(t.$t("app_name")),1),e("form",{onSubmit:s[5]||(s[5]=g((...r)=>d.register&&d.register(...r),["prevent"]))},[e("div",V,[e("label",q,n(t.$t("name")),1),a(e("input",{"onUpdate:modelValue":s[0]||(s[0]=r=>o.name=r),type:"text",class:"w-full border rounded px-3 py-2",required:""},null,512),[[l,o.name]])]),e("div",N,[e("label",S,n(t.$t("email")),1),a(e("input",{"onUpdate:modelValue":s[1]||(s[1]=r=>o.email=r),type:"email",class:"w-full border rounded px-3 py-2",required:""},null,512),[[l,o.email]])]),e("div",U,[e("label",B,n(t.$t("apartment_number")),1),a(e("input",{"onUpdate:modelValue":s[2]||(s[2]=r=>o.apartment_number=r),type:"text",class:"w-full border rounded px-3 py-2",required:""},null,512),[[l,o.apartment_number]])]),e("div",C,[e("label",M,n(t.$t("password")),1),a(e("input",{"onUpdate:modelValue":s[3]||(s[3]=r=>o.password=r),type:"password",class:"w-full border rounded px-3 py-2",required:""},null,512),[[l,o.password]])]),e("div",D,[e("label",I,n(t.$t("confirm_password")),1),a(e("input",{"onUpdate:modelValue":s[4]||(s[4]=r=>o.password_confirmation=r),type:"password",class:"w-full border rounded px-3 py-2",required:""},null,512),[[l,o.password_confirmation]])]),o.error?(p(),m("div",R,n(o.error),1)):_("",!0),e("button",T,n(t.$t("register")),1),e("div",E,[w(u,{to:"/login",class:"text-indigo-600 hover:underline"},{default:f(()=>[y(n(t.$t("login")),1)]),_:1})])],32)])}const j=c(x,[["render",J]]);export{j as default};
