import{_ as u,i as c,c as l,a as s,t as i,b as h,w as d,v as m,d as b,o as p}from"./app-B62tIoiF.js";const g={mixins:[c],data(){return{email:"",password:"",error:""}},methods:{async login(){var o,e;this.error="";try{const t=await this.$axios.post("/login",{email:this.email,password:this.password});localStorage.setItem("token",t.data.token),localStorage.setItem("user",JSON.stringify(t.data.user)),t.data.user.role==="admin"||t.data.user.role==="super_admin"?this.$router.push("/admin"):this.$router.push("/neighbor")}catch(t){this.error=((e=(o=t.response)==null?void 0:o.data)==null?void 0:e.message)||"Lo<PERSON> failed."}}}},w={class:"max-w-md mx-auto bg-white p-8 rounded shadow"},_={class:"text-2xl font-bold mb-6 text-center"},f={class:"mb-4"},x={class:"block mb-1"},y={class:"mb-4"},v={class:"block mb-1"},k={key:0,class:"text-red-600 mb-4"},S={type:"submit",class:"w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700"};function V(o,e,t,$,r,n){return p(),l("div",w,[s("h2",_,i(o.$t("login"))+" - "+i(o.$t("app_name")),1),s("form",{onSubmit:e[2]||(e[2]=b((...a)=>n.login&&n.login(...a),["prevent"]))},[s("div",f,[s("label",x,i(o.$t("email")),1),d(s("input",{"onUpdate:modelValue":e[0]||(e[0]=a=>r.email=a),type:"email",class:"w-full border rounded px-3 py-2",required:""},null,512),[[m,r.email]])]),s("div",y,[s("label",v,i(o.$t("password")),1),d(s("input",{"onUpdate:modelValue":e[1]||(e[1]=a=>r.password=a),type:"password",class:"w-full border rounded px-3 py-2",required:""},null,512),[[m,r.password]])]),r.error?(p(),l("div",k,i(r.error),1)):h("",!0),s("button",S,i(o.$t("login")),1)],32)])}const M=u(g,[["render",V]]);export{M as default};
