import{D as I}from"./DashboardLayout-HXqH_ZDc.js";import{D as M}from"./DataTable-BdcmlClz.js";import{I as D}from"./IncomeForm-CTQWsuWp.js";import{N as P}from"./Notification-II7oLsx2.js";import{_ as S,i as C,h as p,f as r,r as g,o as m,a as t,e as d,c as f,b as _,w as y,t as o,v as b,g as h}from"./app-B62tIoiF.js";const N={mixins:[C],components:{DashboardLayout:I,DataTable:M,IncomeForm:D,Notification:P},data(){return{loading:!1,incomes:[],selectedIncome:null,showEditModal:!1,showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:"",user:null,isSuperAdmin:!1,userLoaded:!1,filters:{date_from:"",date_to:""},pagination:{currentPage:1,lastPage:1,from:0,to:0,total:0},columns:[]}},computed:{totalIncome(){return this.incomes.reduce((e,s)=>e+parseFloat(s.amount),0)},thisMonthIncome(){const e=new Date().getMonth()+1,s=new Date().getFullYear();return this.incomes.filter(l=>{const u=new Date(l.payment_date);return u.getMonth()+1===e&&u.getFullYear()===s}).reduce((l,u)=>l+parseFloat(u.amount),0)},last30DaysIncome(){const e=new Date;return e.setDate(e.getDate()-30),this.incomes.filter(s=>new Date(s.payment_date)>=e).reduce((s,l)=>s+parseFloat(l.amount),0)}},async created(){this.initializeColumns(),this.initializeUserFromStorage(),await this.fetchUser(),this.loadIncomes()},methods:{initializeColumns(){this.columns=[{key:"user.name",label:this.$t("neighbor")},{key:"user.apartment_number",label:this.$t("apartment")},{key:"amount",label:this.$t("amount")},{key:"payment_date",label:this.$t("payment_date")},{key:"payment_method",label:this.$t("method")},{key:"notes",label:this.$t("notes")}]},initializeUserFromStorage(){try{const e=JSON.parse(localStorage.getItem("user")||"null");e&&e.role&&(this.isSuperAdmin=e.role==="super_admin",this.userLoaded=!0)}catch(e){console.error("Error parsing user data from localStorage:",e)}},async fetchUser(){try{const e=await this.$axios.get("/user");this.user=e.data,this.isSuperAdmin=this.user.role==="super_admin",this.userLoaded=!0,localStorage.setItem("user",JSON.stringify(this.user))}catch(e){console.error("Error fetching user:",e)}},async loadIncomes(){this.loading=!0;try{const e=await this.$axios.get("/incomes",{params:{page:this.pagination.currentPage,...this.filters}});this.incomes=e.data.data,this.pagination={currentPage:e.data.current_page,lastPage:e.data.last_page,from:e.data.from,to:e.data.to,total:e.data.total}}catch{this.showError(this.$t("error_loading_incomes"))}finally{this.loading=!1}},applyFilters(){this.pagination.currentPage=1,this.loadIncomes()},previousPage(){this.pagination.currentPage>1&&(this.pagination.currentPage--,this.loadIncomes())},nextPage(){this.pagination.currentPage<this.pagination.lastPage&&(this.pagination.currentPage++,this.loadIncomes())},editIncome(e){this.selectedIncome=e,this.showEditModal=!0},async deleteIncome(e){if(confirm(this.$t("confirm_delete_income")))try{await this.$axios.delete(`/incomes/${e.id}`),this.showSuccess(this.$t("deleted"),this.$t("income_deleted")),this.loadIncomes()}catch{this.showError(this.$t("delete_failed"),this.$t("failed_delete_income"))}},handleEditSuccess(){this.showSuccess(this.$t("updated"),this.$t("income_updated")),this.closeEditModal(),this.loadIncomes()},handleEditError(e){this.showError(this.$t("update_failed"),e)},closeEditModal(){this.showEditModal=!1,this.selectedIncome=null},showSuccess(e,s){this.notificationType="success",this.notificationTitle=e,this.notificationMessage=s,this.showNotification=!0},showError(e,s){this.notificationType="error",this.notificationTitle=e,this.notificationMessage=s,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},F={key:0},T={key:1,class:"text-gray-500 text-sm"},$={class:"bg-white p-6 rounded-lg shadow-sm mb-6"},L={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},z={class:"block text-sm font-medium text-gray-700 mb-2"},A=["dir"],U={class:"block text-sm font-medium text-gray-700 mb-2"},V=["dir"],B={class:"flex items-end"},j={class:"mt-4 flex justify-between items-center"},Y={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"},J={class:"bg-white p-6 rounded-lg shadow-sm"},O={class:"text-lg font-semibold text-gray-700"},R={class:"text-3xl font-bold text-green-600"},q={class:"bg-white p-6 rounded-lg shadow-sm"},G={class:"text-lg font-semibold text-gray-700"},H={class:"text-3xl font-bold text-blue-600"},K={class:"bg-white p-6 rounded-lg shadow-sm"},Q={class:"text-lg font-semibold text-gray-700"},W={class:"text-3xl font-bold text-purple-600"},X={class:"flex space-x-2"},Z=["onClick"],ee=["onClick"],te={class:"mt-6 flex justify-between items-center"},se={class:"text-sm text-gray-700"},oe={class:"flex space-x-2"},ie=["disabled"],ne=["disabled"],ae={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},re={class:"flex items-center justify-center min-h-screen p-4"},le={class:"relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto"},de={class:"px-6 py-4 border-b border-gray-200"},ce={class:"text-lg font-semibold text-gray-900"},me={class:"p-6"};function he(e,s,l,u,i,n){const c=g("router-link"),x=g("data-table"),w=g("income-form"),v=g("notification"),k=g("dashboard-layout");return m(),p(k,{title:e.$t("income_management")},{sidebar:r(()=>[i.userLoaded?(m(),f("div",F,[d(c,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:r(()=>[h(o(e.$t("expenses")),1)]),_:1}),d(c,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:r(()=>[h(o(e.$t("incomes")),1)]),_:1}),d(c,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:r(()=>[h(o(e.$t("users")),1)]),_:1}),i.isSuperAdmin?(m(),p(c,{key:0,to:"/admin/buildings",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:r(()=>[h(o(e.$t("buildings")),1)]),_:1})):_("",!0),i.isSuperAdmin?_("",!0):(m(),p(c,{key:1,to:"/admin/my-building",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:r(()=>[h(o(e.$t("my_building")),1)]),_:1}))])):(m(),f("div",T,o(e.$t("loading_menu")),1))]),default:r(()=>[t("div",$,[t("div",L,[t("div",null,[t("label",z,o(e.$t("from_date")),1),y(t("input",{type:"date","onUpdate:modelValue":s[0]||(s[0]=a=>i.filters.date_from=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md",dir:e.$isRTL()?"rtl":"ltr"},null,8,A),[[b,i.filters.date_from]])]),t("div",null,[t("label",U,o(e.$t("to_date")),1),y(t("input",{type:"date","onUpdate:modelValue":s[1]||(s[1]=a=>i.filters.date_to=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md",dir:e.$isRTL()?"rtl":"ltr"},null,8,V),[[b,i.filters.date_to]])]),t("div",B,[t("button",{onClick:s[2]||(s[2]=(...a)=>n.applyFilters&&n.applyFilters(...a)),class:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"},o(e.$t("apply_filters")),1)])]),t("div",j,[d(c,{to:"/admin/incomes/create",class:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"},{default:r(()=>[h(o(e.$t("record_new_income")),1)]),_:1})])]),t("div",Y,[t("div",J,[t("h3",O,o(e.$t("total_income")),1),t("p",R,"₪"+o(n.totalIncome.toFixed(2)),1)]),t("div",q,[t("h3",G,o(e.$t("this_month")),1),t("p",H,"₪"+o(n.thisMonthIncome.toFixed(2)),1)]),t("div",K,[t("h3",Q,o(e.$t("last_30_days")),1),t("p",W,"₪"+o(n.last30DaysIncome.toFixed(2)),1)])]),d(x,{title:e.$t("income_records"),columns:i.columns,items:i.incomes,loading:i.loading},{actions:r(({item:a})=>[t("div",X,[t("button",{onClick:E=>n.editIncome(a),class:"text-indigo-600 hover:text-indigo-900"},o(e.$t("edit")),9,Z),t("button",{onClick:E=>n.deleteIncome(a),class:"text-red-600 hover:text-red-900"},o(e.$t("delete")),9,ee)])]),_:1},8,["title","columns","items","loading"]),t("div",te,[t("div",se,o(e.$t("showing_results"))+" "+o(i.pagination.from)+" "+o(e.$t("to"))+" "+o(i.pagination.to)+" "+o(e.$t("of"))+" "+o(i.pagination.total)+" "+o(e.$t("results")),1),t("div",oe,[t("button",{onClick:s[3]||(s[3]=(...a)=>n.previousPage&&n.previousPage(...a)),disabled:i.pagination.currentPage===1,class:"px-3 py-1 border rounded disabled:opacity-50"},o(e.$t("previous")),9,ie),t("button",{onClick:s[4]||(s[4]=(...a)=>n.nextPage&&n.nextPage(...a)),disabled:i.pagination.currentPage===i.pagination.lastPage,class:"px-3 py-1 border rounded disabled:opacity-50"},o(e.$t("next")),9,ne)])]),i.showEditModal?(m(),f("div",ae,[t("div",{class:"fixed inset-0 bg-black bg-opacity-50",onClick:s[5]||(s[5]=(...a)=>n.closeEditModal&&n.closeEditModal(...a))}),t("div",re,[t("div",le,[t("button",{onClick:s[6]||(s[6]=(...a)=>n.closeEditModal&&n.closeEditModal(...a)),class:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"},s[7]||(s[7]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),t("div",de,[t("h3",ce,o(e.$t("edit_income")),1)]),t("div",me,[d(w,{income:i.selectedIncome,"is-edit":!0,onSuccess:n.handleEditSuccess,onError:n.handleEditError,onCancel:n.closeEditModal},null,8,["income","onSuccess","onError","onCancel"])])])])])):_("",!0),d(v,{show:i.showNotification,type:i.notificationType,title:i.notificationTitle,message:i.notificationMessage,onClose:n.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1},8,["title"])}const ye=S(N,[["render",he]]);export{ye as default};
