import{D as w}from"./DashboardLayout-HXqH_ZDc.js";import{B as v}from"./BuildingForm-BCC_xiW2.js";import{N as E}from"./Notification-II7oLsx2.js";import{_ as k,i as M,h as y,f as d,r as u,o as l,c as a,b as m,e as g,a as e,t as s,g as h}from"./app-B62tIoiF.js";const S={mixins:[M],components:{DashboardLayout:w,BuildingForm:v,Notification:E},data(){return{loading:!1,building:null,showEditModal:!1,generatingExpenses:!1,showNotification:!1,notificationType:"info",notificationTitle:"",notificationMessage:"",userLoaded:!1,isSuperAdmin:!1}},mounted(){this.initializeUserFromStorage(),this.loadBuilding()},methods:{initializeUserFromStorage(){try{const t=JSON.parse(localStorage.getItem("user")||"null");t&&t.role&&(this.isSuperAdmin=t.role==="super_admin",this.userLoaded=!0)}catch(t){console.error("Error parsing user data from localStorage:",t)}},async loadBuilding(){var t;this.loading=!0;try{const o=await this.$axios.get("/my-building");this.building=o.data}catch(o){console.error("Error loading building:",o),((t=o.response)==null?void 0:t.status)!==404&&this.showError(this.$t("error"),this.$t("failed_load_building_info"))}finally{this.loading=!1}},editBuilding(){this.showEditModal=!0},closeEditModal(){this.showEditModal=!1},handleEditSuccess(t){this.showSuccess(this.$t("success"),this.$t("building_updated")),this.closeEditModal(),this.loadBuilding()},handleEditError(t){this.showError(this.$t("error"),t)},async generateMonthlyExpenses(){var i,n;const t=new Date().getMonth()+1,o=new Date().getFullYear(),_=t.toString().padStart(2,"0"),f=parseFloat(this.building.monthly_fee).toFixed(2);if(confirm(this.$t("confirm_generate_monthly_expenses",{fee:f,month:_,year:o}))){this.generatingExpenses=!0;try{await this.$axios.post("/expenses/generate-monthly",{month:_,year:o.toString()}),this.showSuccess(this.$t("generated"),this.$t("monthly_expenses_generated"))}catch(r){this.showError(this.$t("generation_failed"),((n=(i=r.response)==null?void 0:i.data)==null?void 0:n.message)||this.$t("failed_generate_monthly_expenses"))}finally{this.generatingExpenses=!1}}},showSuccess(t,o){this.notificationType="success",this.notificationTitle=t,this.notificationMessage=o,this.showNotification=!0},showError(t,o){this.notificationType="error",this.notificationTitle=t,this.notificationMessage=o,this.showNotification=!0},closeNotification(){this.showNotification=!1}}},B={key:0},N={key:1,class:"text-gray-500 text-sm"},F={key:0,class:"text-center py-4"},$={key:1,class:"max-w-4xl mx-auto"},C={class:"bg-white p-6 rounded-lg shadow-sm mb-6"},T={class:"flex justify-between items-center mb-4"},D={class:"text-xl font-semibold text-gray-900"},L={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},j={class:"text-sm font-medium text-gray-500"},A={class:"mt-1 text-sm text-gray-900"},V={class:"text-sm font-medium text-gray-500"},z={class:"mt-1 text-sm text-gray-900"},U={class:"text-sm font-medium text-gray-500"},Y={class:"mt-1 text-sm text-gray-900"},I={class:"text-sm font-medium text-gray-500"},J={class:"mt-1 text-sm text-gray-900"},O={class:"text-sm font-medium text-gray-500"},q={class:"mt-1 text-sm text-gray-900"},G={class:"text-sm font-medium text-gray-500"},H={class:"mt-1 text-sm text-gray-900"},K={key:0,class:"mt-6"},P={class:"text-sm font-medium text-gray-500"},Q={class:"mt-1 text-sm text-gray-900"},R={class:"bg-white p-6 rounded-lg shadow-sm"},W={class:"text-xl font-semibold text-gray-900 mb-4"},X={class:"text-sm text-gray-600 mb-4"},Z={class:"flex items-center space-x-4"},tt=["disabled"],et={key:2,class:"text-center py-8"},st={class:"text-red-500"},it={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center"},ot={class:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4"},nt={class:"flex justify-between items-center p-6 border-b"},lt={class:"text-xl font-semibold text-gray-900"},rt={class:"p-6"};function dt(t,o,_,f,i,n){const r=u("router-link"),p=u("building-form"),b=u("notification"),x=u("dashboard-layout");return l(),y(x,{title:t.$t("my_building")},{sidebar:d(()=>[i.userLoaded?(l(),a("div",B,[g(r,{to:"/admin/expenses",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[h(s(t.$t("expenses")),1)]),_:1}),g(r,{to:"/admin/incomes",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[h(s(t.$t("incomes")),1)]),_:1}),g(r,{to:"/admin/users",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[h(s(t.$t("users")),1)]),_:1}),i.isSuperAdmin?(l(),y(r,{key:0,to:"/admin/buildings",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[h(s(t.$t("buildings")),1)]),_:1})):m("",!0),i.isSuperAdmin?m("",!0):(l(),y(r,{key:1,to:"/admin/my-building",class:"block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded","active-class":"bg-indigo-50 text-indigo-600"},{default:d(()=>[h(s(t.$t("my_building")),1)]),_:1}))])):(l(),a("div",N,s(t.$t("loading_menu")),1))]),default:d(()=>[i.loading?(l(),a("div",F,[e("p",null,s(t.$t("loading")),1)])):i.building?(l(),a("div",$,[e("div",C,[e("div",T,[e("h2",D,s(t.$t("building_information")),1),e("button",{onClick:o[0]||(o[0]=(...c)=>n.editBuilding&&n.editBuilding(...c)),class:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"},s(t.$t("edit_building")),1)]),e("div",L,[e("div",null,[e("h3",j,s(t.$t("building_name")),1),e("p",A,s(i.building.name),1)]),e("div",null,[e("h3",V,s(t.$t("monthly_fee")),1),e("p",z,"₪"+s(parseFloat(i.building.monthly_fee).toFixed(2)),1)]),e("div",null,[e("h3",U,s(t.$t("address")),1),e("p",Y,s(i.building.address||t.$t("not_specified")),1)]),e("div",null,[e("h3",I,s(t.$t("city")),1),e("p",J,s(i.building.city||t.$t("not_specified")),1)]),e("div",null,[e("h3",O,s(t.$t("country")),1),e("p",q,s(i.building.country||t.$t("not_specified")),1)]),e("div",null,[e("h3",G,s(t.$t("postal_code")),1),e("p",H,s(i.building.postal_code||t.$t("not_specified")),1)])]),i.building.description?(l(),a("div",K,[e("h3",P,s(t.$t("description")),1),e("p",Q,s(i.building.description),1)])):m("",!0)]),e("div",R,[e("h2",W,s(t.$t("monthly_expense_generation")),1),e("p",X,s(t.$t("generate_monthly_expenses_description"))+" "+s(t.$t("each_neighbor_charged"))+" ₪"+s(parseFloat(i.building.monthly_fee).toFixed(2))+". ",1),e("div",Z,[e("button",{onClick:o[1]||(o[1]=(...c)=>n.generateMonthlyExpenses&&n.generateMonthlyExpenses(...c)),disabled:i.generatingExpenses,class:"px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-gray-400"},s(i.generatingExpenses?t.$t("generating"):`${t.$t("generate_monthly_expenses")} (₪${parseFloat(i.building.monthly_fee).toFixed(2)})`),9,tt)])])])):(l(),a("div",et,[e("p",st,s(t.$t("no_building_assigned")),1)])),i.showEditModal?(l(),a("div",it,[e("div",ot,[e("div",nt,[e("h2",lt,s(t.$t("edit_building")),1),e("button",{onClick:o[2]||(o[2]=(...c)=>n.closeEditModal&&n.closeEditModal(...c)),class:"text-gray-400 hover:text-gray-600"},o[3]||(o[3]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",rt,[g(p,{building:i.building,"is-edit":!0,"is-my-building":!0,onSuccess:n.handleEditSuccess,onError:n.handleEditError,onCancel:n.closeEditModal},null,8,["building","onSuccess","onError","onCancel"])])])])):m("",!0),g(b,{show:i.showNotification,type:i.notificationType,title:i.notificationTitle,message:i.notificationMessage,onClose:n.closeNotification},null,8,["show","type","title","message","onClose"])]),_:1},8,["title"])}const ut=k(S,[["render",dt]]);export{ut as default};
