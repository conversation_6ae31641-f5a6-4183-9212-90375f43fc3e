import{_ as b,i as f,c as d,b as h,a as t,t as n,w as a,j as p,F as y,k as g,v as m,d as _,o as l}from"./app-B62tIoiF.js";const v={name:"IncomeForm",mixins:[f],props:{income:{type:Object,default:()=>({user_id:"",amount:"",payment_date:new Date().toISOString().split("T")[0],payment_method:"cash",notes:""})},isEdit:{type:Boolean,default:!1}},data(){return{processing:!1,neighbors:[],formData:{user_id:"",amount:"",payment_date:new Date().toISOString().split("T")[0],payment_method:"cash",notes:""}}},watch:{income:{handler(e){e&&this.isEdit&&(this.formData={user_id:e.user_id||"",amount:e.amount||"",payment_date:e.payment_date||new Date().toISOString().split("T")[0],payment_method:e.payment_method||"cash",notes:e.notes||""})},immediate:!0,deep:!0}},created(){this.fetchNeighbors()},mounted(){this.isEdit&&this.income&&(this.formData={user_id:this.income.user_id||"",amount:this.income.amount||"",payment_date:this.income.payment_date||new Date().toISOString().split("T")[0],payment_method:this.income.payment_method||"cash",notes:this.income.notes||""})},methods:{async fetchNeighbors(){try{const e=await this.$axios.get("/admin/users"),o=e.data.data||e.data;this.neighbors=o.filter(r=>r.role==="neighbor")}catch(e){console.error("Error fetching neighbors:",e)}},async handleSubmit(){var e,o;this.processing=!0;try{const r={...this.formData},u=await this.$axios[this.isEdit?"put":"post"](this.isEdit?`/incomes/${this.income.id}`:"/incomes",r);this.$emit("success",u.data)}catch(r){this.$emit("error",((o=(e=r.response)==null?void 0:e.data)==null?void 0:o.message)||"Failed to save income")}finally{this.processing=!1}}}},D={key:0,class:"text-xl font-semibold text-gray-900 mb-6"},x={for:"user_id",class:"block text-sm font-medium text-gray-700 mb-2"},w={value:""},k=["value"],S={for:"amount",class:"block text-sm font-medium text-gray-700 mb-2"},$={class:"relative"},E={for:"payment_date",class:"block text-sm font-medium text-gray-700 mb-2"},V={for:"payment_method",class:"block text-sm font-medium text-gray-700 mb-2"},U={value:"cash"},F={value:"bank_transfer"},O={value:"check"},T={for:"notes",class:"block text-sm font-medium text-gray-700 mb-2"},q=["placeholder"],B={class:"flex justify-end space-x-3 pt-4"},M=["disabled"];function N(e,o,r,u,i,c){return l(),d("div",null,[r.isEdit?h("",!0):(l(),d("h2",D,n(e.$t("create_income")),1)),t("form",{onSubmit:o[6]||(o[6]=_((...s)=>c.handleSubmit&&c.handleSubmit(...s),["prevent"])),class:"space-y-6"},[t("div",null,[t("label",x,n(e.$t("neighbor")),1),a(t("select",{id:"user_id","onUpdate:modelValue":o[0]||(o[0]=s=>i.formData.user_id=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},[t("option",w,n(e.$t("neighbor")),1),(l(!0),d(y,null,g(i.neighbors,s=>(l(),d("option",{key:s.id,value:s.id},n(s.name)+" ("+n(e.$t("apartment_number"))+": "+n(s.apartment_number)+") ",9,k))),128))],512),[[p,i.formData.user_id]])]),t("div",null,[t("label",S,n(e.$t("amount")),1),t("div",$,[o[7]||(o[7]=t("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[t("span",{class:"text-gray-500 text-sm"},"₪")],-1)),a(t("input",{type:"number",id:"amount","onUpdate:modelValue":o[1]||(o[1]=s=>i.formData.amount=s),class:"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00",step:"0.01",min:"0",required:""},null,512),[[m,i.formData.amount]])])]),t("div",null,[t("label",E,n(e.$t("payment_date")),1),a(t("input",{type:"date",id:"payment_date","onUpdate:modelValue":o[2]||(o[2]=s=>i.formData.payment_date=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},null,512),[[m,i.formData.payment_date]])]),t("div",null,[t("label",V,n(e.$t("payment_method")),1),a(t("select",{id:"payment_method","onUpdate:modelValue":o[3]||(o[3]=s=>i.formData.payment_method=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},[t("option",U,n(e.$t("cash")),1),t("option",F,n(e.$t("bank_transfer")),1),t("option",O,n(e.$t("check")),1)],512),[[p,i.formData.payment_method]])]),t("div",null,[t("label",T,n(e.$t("notes")),1),a(t("textarea",{id:"notes","onUpdate:modelValue":o[4]||(o[4]=s=>i.formData.notes=s),rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none",placeholder:e.$t("notes")},null,8,q),[[m,i.formData.notes]])]),t("div",B,[t("button",{type:"button",onClick:o[5]||(o[5]=s=>e.$emit("cancel")),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"},n(e.$t("cancel")),1),t("button",{type:"submit",disabled:i.processing,class:"px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"},n(i.processing?e.$t("loading"):r.isEdit?e.$t("edit_income"):e.$t("create_income")),9,M)])],32)])}const C=b(v,[["render",N]]);export{C as I};
