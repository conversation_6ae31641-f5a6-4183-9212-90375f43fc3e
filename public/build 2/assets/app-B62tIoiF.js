const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AdminDashboard-BMtttaIa.js","assets/DashboardLayout-HXqH_ZDc.js","assets/DataTable-BdcmlClz.js","assets/NeighborDashboard-CqbBwD0w.js","assets/Notification-II7oLsx2.js","assets/UserManagement-G97gsK7c.js","assets/UserForm-oXxcTwhj.js","assets/CreateUser-CFsZk7kt.js","assets/MyBuilding-2VWMq-A2.js","assets/BuildingForm-BCC_xiW2.js","assets/ExpenseManagement-D0R5vrQp.js","assets/ExpenseForm-B2fnylaP.js","assets/CreateExpense-4z0eaws0.js","assets/IncomeManagement-CylQxRx_.js","assets/IncomeForm-CTQWsuWp.js","assets/CreateIncome-CA-TnWaY.js","assets/BuildingManagement-Dv9thyRK.js","assets/CreateBuilding-27nUgw7_.js","assets/EditBuilding-B2e1VlX9.js"])))=>i.map(i=>d[i]);
function jo(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ml}=Object.prototype,{getPrototypeOf:ss}=Object,{iterator:er,toStringTag:$o}=Symbol,tr=(e=>t=>{const n=Ml.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),ke=e=>(e=e.toLowerCase(),t=>tr(t)===e),nr=e=>t=>typeof t===e,{isArray:Jt}=Array,hn=nr("undefined");function Dl(e){return e!==null&&!hn(e)&&e.constructor!==null&&!hn(e.constructor)&&ve(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ho=ke("ArrayBuffer");function Ul(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ho(e.buffer),t}const kl=nr("string"),ve=nr("function"),qo=nr("number"),rr=e=>e!==null&&typeof e=="object",Bl=e=>e===!0||e===!1,In=e=>{if(tr(e)!=="object")return!1;const t=ss(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!($o in e)&&!(er in e)},jl=ke("Date"),$l=ke("File"),Hl=ke("Blob"),ql=ke("FileList"),Vl=e=>rr(e)&&ve(e.pipe),Kl=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ve(e.append)&&((t=tr(e))==="formdata"||t==="object"&&ve(e.toString)&&e.toString()==="[object FormData]"))},Wl=ke("URLSearchParams"),[zl,Gl,Jl,Xl]=["ReadableStream","Request","Response","Headers"].map(ke),Yl=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function vn(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),Jt(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(r=0;r<i;r++)l=o[r],t.call(null,e[l],l,e)}}function Vo(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const Rt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ko=e=>!hn(e)&&e!==Rt;function Br(){const{caseless:e}=Ko(this)&&this||{},t={},n=(r,s)=>{const o=e&&Vo(t,s)||s;In(t[o])&&In(r)?t[o]=Br(t[o],r):In(r)?t[o]=Br({},r):Jt(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&vn(arguments[r],n);return t}const Ql=(e,t,n,{allOwnKeys:r}={})=>(vn(t,(s,o)=>{n&&ve(s)?e[o]=jo(s,n):e[o]=s},{allOwnKeys:r}),e),Zl=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),ea=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},ta=(e,t,n,r)=>{let s,o,i;const l={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&ss(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},na=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},ra=e=>{if(!e)return null;if(Jt(e))return e;let t=e.length;if(!qo(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},sa=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ss(Uint8Array)),oa=(e,t)=>{const r=(e&&e[er]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},ia=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},la=ke("HTMLFormElement"),aa=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Ps=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ca=ke("RegExp"),Wo=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};vn(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},ua=e=>{Wo(e,(t,n)=>{if(ve(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(ve(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},fa=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return Jt(e)?r(e):r(String(e).split(t)),n},da=()=>{},ha=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function pa(e){return!!(e&&ve(e.append)&&e[$o]==="FormData"&&e[er])}const ma=e=>{const t=new Array(10),n=(r,s)=>{if(rr(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=Jt(r)?[]:{};return vn(r,(i,l)=>{const a=n(i,s+1);!hn(a)&&(o[l]=a)}),t[s]=void 0,o}}return r};return n(e,0)},ga=ke("AsyncFunction"),_a=e=>e&&(rr(e)||ve(e))&&ve(e.then)&&ve(e.catch),zo=((e,t)=>e?setImmediate:t?((n,r)=>(Rt.addEventListener("message",({source:s,data:o})=>{s===Rt&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),Rt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",ve(Rt.postMessage)),ya=typeof queueMicrotask<"u"?queueMicrotask.bind(Rt):typeof process<"u"&&process.nextTick||zo,ba=e=>e!=null&&ve(e[er]),_={isArray:Jt,isArrayBuffer:Ho,isBuffer:Dl,isFormData:Kl,isArrayBufferView:Ul,isString:kl,isNumber:qo,isBoolean:Bl,isObject:rr,isPlainObject:In,isReadableStream:zl,isRequest:Gl,isResponse:Jl,isHeaders:Xl,isUndefined:hn,isDate:jl,isFile:$l,isBlob:Hl,isRegExp:ca,isFunction:ve,isStream:Vl,isURLSearchParams:Wl,isTypedArray:sa,isFileList:ql,forEach:vn,merge:Br,extend:Ql,trim:Yl,stripBOM:Zl,inherits:ea,toFlatObject:ta,kindOf:tr,kindOfTest:ke,endsWith:na,toArray:ra,forEachEntry:oa,matchAll:ia,isHTMLForm:la,hasOwnProperty:Ps,hasOwnProp:Ps,reduceDescriptors:Wo,freezeMethods:ua,toObjectSet:fa,toCamelCase:aa,noop:da,toFiniteNumber:ha,findKey:Vo,global:Rt,isContextDefined:Ko,isSpecCompliantForm:pa,toJSONObject:ma,isAsyncFn:ga,isThenable:_a,setImmediate:zo,asap:ya,isIterable:ba};function q(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}_.inherits(q,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:_.toJSONObject(this.config),code:this.code,status:this.status}}});const Go=q.prototype,Jo={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Jo[e]={value:e}});Object.defineProperties(q,Jo);Object.defineProperty(Go,"isAxiosError",{value:!0});q.from=(e,t,n,r,s,o)=>{const i=Object.create(Go);return _.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),q.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const wa=null;function jr(e){return _.isPlainObject(e)||_.isArray(e)}function Xo(e){return _.endsWith(e,"[]")?e.slice(0,-2):e}function Ls(e,t,n){return e?e.concat(t).map(function(s,o){return s=Xo(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function Ea(e){return _.isArray(e)&&!e.some(jr)}const xa=_.toFlatObject(_,{},null,function(t){return/^is[A-Z]/.test(t)});function sr(e,t,n){if(!_.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=_.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,S){return!_.isUndefined(S[v])});const r=n.metaTokens,s=n.visitor||c,o=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&_.isSpecCompliantForm(t);if(!_.isFunction(s))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(_.isDate(y))return y.toISOString();if(_.isBoolean(y))return y.toString();if(!a&&_.isBlob(y))throw new q("Blob is not supported. Use a Buffer instead.");return _.isArrayBuffer(y)||_.isTypedArray(y)?a&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function c(y,v,S){let P=y;if(y&&!S&&typeof y=="object"){if(_.endsWith(v,"{}"))v=r?v:v.slice(0,-2),y=JSON.stringify(y);else if(_.isArray(y)&&Ea(y)||(_.isFileList(y)||_.endsWith(v,"[]"))&&(P=_.toArray(y)))return v=Xo(v),P.forEach(function(I,F){!(_.isUndefined(I)||I===null)&&t.append(i===!0?Ls([v],F,o):i===null?v:v+"[]",u(I))}),!1}return jr(y)?!0:(t.append(Ls(S,v,o),u(y)),!1)}const f=[],p=Object.assign(xa,{defaultVisitor:c,convertValue:u,isVisitable:jr});function g(y,v){if(!_.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+v.join("."));f.push(y),_.forEach(y,function(P,C){(!(_.isUndefined(P)||P===null)&&s.call(t,P,_.isString(C)?C.trim():C,v,p))===!0&&g(P,v?v.concat(C):[C])}),f.pop()}}if(!_.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Ns(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function os(e,t){this._pairs=[],e&&sr(e,this,t)}const Yo=os.prototype;Yo.append=function(t,n){this._pairs.push([t,n])};Yo.toString=function(t){const n=t?function(r){return t.call(this,r,Ns)}:Ns;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function va(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Qo(e,t,n){if(!t)return e;const r=n&&n.encode||va;_.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=_.isURLSearchParams(t)?t.toString():new os(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Is{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){_.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Zo={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Sa=typeof URLSearchParams<"u"?URLSearchParams:os,Ra=typeof FormData<"u"?FormData:null,Aa=typeof Blob<"u"?Blob:null,Ta={isBrowser:!0,classes:{URLSearchParams:Sa,FormData:Ra,Blob:Aa},protocols:["http","https","file","blob","url","data"]},is=typeof window<"u"&&typeof document<"u",$r=typeof navigator=="object"&&navigator||void 0,Ca=is&&(!$r||["ReactNative","NativeScript","NS"].indexOf($r.product)<0),Oa=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Pa=is&&window.location.href||"http://localhost",La=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:is,hasStandardBrowserEnv:Ca,hasStandardBrowserWebWorkerEnv:Oa,navigator:$r,origin:Pa},Symbol.toStringTag,{value:"Module"})),fe={...La,...Ta};function Na(e,t){return sr(e,new fe.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return fe.isNode&&_.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Ia(e){return _.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Fa(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function ei(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=o>=n.length;return i=!i&&_.isArray(s)?s.length:i,a?(_.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!l):((!s[i]||!_.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&_.isArray(s[i])&&(s[i]=Fa(s[i])),!l)}if(_.isFormData(e)&&_.isFunction(e.entries)){const n={};return _.forEachEntry(e,(r,s)=>{t(Ia(r),s,n,0)}),n}return null}function Ma(e,t,n){if(_.isString(e))try{return(t||JSON.parse)(e),_.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Sn={transitional:Zo,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=_.isObject(t);if(o&&_.isHTMLForm(t)&&(t=new FormData(t)),_.isFormData(t))return s?JSON.stringify(ei(t)):t;if(_.isArrayBuffer(t)||_.isBuffer(t)||_.isStream(t)||_.isFile(t)||_.isBlob(t)||_.isReadableStream(t))return t;if(_.isArrayBufferView(t))return t.buffer;if(_.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Na(t,this.formSerializer).toString();if((l=_.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return sr(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),Ma(t)):t}],transformResponse:[function(t){const n=this.transitional||Sn.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(_.isResponse(t)||_.isReadableStream(t))return t;if(t&&_.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?q.from(l,q.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:fe.classes.FormData,Blob:fe.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};_.forEach(["delete","get","head","post","put","patch"],e=>{Sn.headers[e]={}});const Da=_.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ua=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&Da[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Fs=Symbol("internals");function en(e){return e&&String(e).trim().toLowerCase()}function Fn(e){return e===!1||e==null?e:_.isArray(e)?e.map(Fn):String(e)}function ka(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Ba=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Er(e,t,n,r,s){if(_.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!_.isString(t)){if(_.isString(r))return t.indexOf(r)!==-1;if(_.isRegExp(r))return r.test(t)}}function ja(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function $a(e,t){const n=_.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let Se=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(l,a,u){const c=en(a);if(!c)throw new Error("header name must be a non-empty string");const f=_.findKey(s,c);(!f||s[f]===void 0||u===!0||u===void 0&&s[f]!==!1)&&(s[f||a]=Fn(l))}const i=(l,a)=>_.forEach(l,(u,c)=>o(u,c,a));if(_.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(_.isString(t)&&(t=t.trim())&&!Ba(t))i(Ua(t),n);else if(_.isObject(t)&&_.isIterable(t)){let l={},a,u;for(const c of t){if(!_.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?_.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}i(l,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=en(t),t){const r=_.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return ka(s);if(_.isFunction(n))return n.call(this,s,r);if(_.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=en(t),t){const r=_.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Er(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=en(i),i){const l=_.findKey(r,i);l&&(!n||Er(r,r[l],l,n))&&(delete r[l],s=!0)}}return _.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||Er(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return _.forEach(this,(s,o)=>{const i=_.findKey(r,o);if(i){n[i]=Fn(s),delete n[o];return}const l=t?ja(o):String(o).trim();l!==o&&delete n[o],n[l]=Fn(s),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return _.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&_.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Fs]=this[Fs]={accessors:{}}).accessors,s=this.prototype;function o(i){const l=en(i);r[l]||($a(s,i),r[l]=!0)}return _.isArray(t)?t.forEach(o):o(t),this}};Se.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);_.reduceDescriptors(Se.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});_.freezeMethods(Se);function xr(e,t){const n=this||Sn,r=t||n,s=Se.from(r.headers);let o=r.data;return _.forEach(e,function(l){o=l.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function ti(e){return!!(e&&e.__CANCEL__)}function Xt(e,t,n){q.call(this,e??"canceled",q.ERR_CANCELED,t,n),this.name="CanceledError"}_.inherits(Xt,q,{__CANCEL__:!0});function ni(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new q("Request failed with status code "+n.status,[q.ERR_BAD_REQUEST,q.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Ha(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function qa(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=r[o];i||(i=u),n[s]=a,r[s]=u;let f=o,p=0;for(;f!==s;)p+=n[f++],f=f%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),u-i<t)return;const g=c&&u-c;return g?Math.round(p*1e3/g):void 0}}function Va(e,t){let n=0,r=1e3/t,s,o;const i=(u,c=Date.now())=>{n=c,s=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=r?i(u,c):(s=u,o||(o=setTimeout(()=>{o=null,i(s)},r-f)))},()=>s&&i(s)]}const Vn=(e,t,n=3)=>{let r=0;const s=qa(50,250);return Va(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,a=i-r,u=s(a),c=i<=l;r=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-i)/u:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},Ms=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ds=e=>(...t)=>_.asap(()=>e(...t)),Ka=fe.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,fe.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(fe.origin),fe.navigator&&/(msie|trident)/i.test(fe.navigator.userAgent)):()=>!0,Wa=fe.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];_.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),_.isString(r)&&i.push("path="+r),_.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function za(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ga(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ri(e,t,n){let r=!za(t);return e&&(r||n==!1)?Ga(e,t):t}const Us=e=>e instanceof Se?{...e}:e;function Ct(e,t){t=t||{};const n={};function r(u,c,f,p){return _.isPlainObject(u)&&_.isPlainObject(c)?_.merge.call({caseless:p},u,c):_.isPlainObject(c)?_.merge({},c):_.isArray(c)?c.slice():c}function s(u,c,f,p){if(_.isUndefined(c)){if(!_.isUndefined(u))return r(void 0,u,f,p)}else return r(u,c,f,p)}function o(u,c){if(!_.isUndefined(c))return r(void 0,c)}function i(u,c){if(_.isUndefined(c)){if(!_.isUndefined(u))return r(void 0,u)}else return r(void 0,c)}function l(u,c,f){if(f in t)return r(u,c);if(f in e)return r(void 0,u)}const a={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,c,f)=>s(Us(u),Us(c),f,!0)};return _.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=a[c]||s,p=f(e[c],t[c],c);_.isUndefined(p)&&f!==l||(n[c]=p)}),n}const si=e=>{const t=Ct({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Se.from(i),t.url=Qo(ri(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(_.isFormData(n)){if(fe.hasStandardBrowserEnv||fe.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...c]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(fe.hasStandardBrowserEnv&&(r&&_.isFunction(r)&&(r=r(t)),r||r!==!1&&Ka(t.url))){const u=s&&o&&Wa.read(o);u&&i.set(s,u)}return t},Ja=typeof XMLHttpRequest<"u",Xa=Ja&&function(e){return new Promise(function(n,r){const s=si(e);let o=s.data;const i=Se.from(s.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=s,c,f,p,g,y;function v(){g&&g(),y&&y(),s.cancelToken&&s.cancelToken.unsubscribe(c),s.signal&&s.signal.removeEventListener("abort",c)}let S=new XMLHttpRequest;S.open(s.method.toUpperCase(),s.url,!0),S.timeout=s.timeout;function P(){if(!S)return;const I=Se.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),H={data:!l||l==="text"||l==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:I,config:e,request:S};ni(function(K){n(K),v()},function(K){r(K),v()},H),S=null}"onloadend"in S?S.onloadend=P:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(P)},S.onabort=function(){S&&(r(new q("Request aborted",q.ECONNABORTED,e,S)),S=null)},S.onerror=function(){r(new q("Network Error",q.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let F=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const H=s.transitional||Zo;s.timeoutErrorMessage&&(F=s.timeoutErrorMessage),r(new q(F,H.clarifyTimeoutError?q.ETIMEDOUT:q.ECONNABORTED,e,S)),S=null},o===void 0&&i.setContentType(null),"setRequestHeader"in S&&_.forEach(i.toJSON(),function(F,H){S.setRequestHeader(H,F)}),_.isUndefined(s.withCredentials)||(S.withCredentials=!!s.withCredentials),l&&l!=="json"&&(S.responseType=s.responseType),u&&([p,y]=Vn(u,!0),S.addEventListener("progress",p)),a&&S.upload&&([f,g]=Vn(a),S.upload.addEventListener("progress",f),S.upload.addEventListener("loadend",g)),(s.cancelToken||s.signal)&&(c=I=>{S&&(r(!I||I.type?new Xt(null,e,S):I),S.abort(),S=null)},s.cancelToken&&s.cancelToken.subscribe(c),s.signal&&(s.signal.aborted?c():s.signal.addEventListener("abort",c)));const C=Ha(s.url);if(C&&fe.protocols.indexOf(C)===-1){r(new q("Unsupported protocol "+C+":",q.ERR_BAD_REQUEST,e));return}S.send(o||null)})},Ya=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(u){if(!s){s=!0,l();const c=u instanceof Error?u:this.reason;r.abort(c instanceof q?c:new Xt(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new q(`timeout ${t} of ms exceeded`,q.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:a}=r;return a.unsubscribe=()=>_.asap(l),a}},Qa=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},Za=async function*(e,t){for await(const n of ec(e))yield*Qa(n,t)},ec=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},ks=(e,t,n,r)=>{const s=Za(e,t);let o=0,i,l=a=>{i||(i=!0,r&&r(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await s.next();if(u){l(),a.close();return}let f=c.byteLength;if(n){let p=o+=f;n(p)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),s.return()}},{highWaterMark:2})},or=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",oi=or&&typeof ReadableStream=="function",tc=or&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ii=(e,...t)=>{try{return!!e(...t)}catch{return!1}},nc=oi&&ii(()=>{let e=!1;const t=new Request(fe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Bs=64*1024,Hr=oi&&ii(()=>_.isReadableStream(new Response("").body)),Kn={stream:Hr&&(e=>e.body)};or&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Kn[t]&&(Kn[t]=_.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new q(`Response type '${t}' is not supported`,q.ERR_NOT_SUPPORT,r)})})})(new Response);const rc=async e=>{if(e==null)return 0;if(_.isBlob(e))return e.size;if(_.isSpecCompliantForm(e))return(await new Request(fe.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(_.isArrayBufferView(e)||_.isArrayBuffer(e))return e.byteLength;if(_.isURLSearchParams(e)&&(e=e+""),_.isString(e))return(await tc(e)).byteLength},sc=async(e,t)=>{const n=_.toFiniteNumber(e.getContentLength());return n??rc(t)},oc=or&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:p}=si(e);u=u?(u+"").toLowerCase():"text";let g=Ya([s,o&&o.toAbortSignal()],i),y;const v=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let S;try{if(a&&nc&&n!=="get"&&n!=="head"&&(S=await sc(c,r))!==0){let H=new Request(t,{method:"POST",body:r,duplex:"half"}),ee;if(_.isFormData(r)&&(ee=H.headers.get("content-type"))&&c.setContentType(ee),H.body){const[K,ge]=Ms(S,Vn(Ds(a)));r=ks(H.body,Bs,K,ge)}}_.isString(f)||(f=f?"include":"omit");const P="credentials"in Request.prototype;y=new Request(t,{...p,signal:g,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:P?f:void 0});let C=await fetch(y,p);const I=Hr&&(u==="stream"||u==="response");if(Hr&&(l||I&&v)){const H={};["status","statusText","headers"].forEach(Ie=>{H[Ie]=C[Ie]});const ee=_.toFiniteNumber(C.headers.get("content-length")),[K,ge]=l&&Ms(ee,Vn(Ds(l),!0))||[];C=new Response(ks(C.body,Bs,K,()=>{ge&&ge(),v&&v()}),H)}u=u||"text";let F=await Kn[_.findKey(Kn,u)||"text"](C,e);return!I&&v&&v(),await new Promise((H,ee)=>{ni(H,ee,{data:F,headers:Se.from(C.headers),status:C.status,statusText:C.statusText,config:e,request:y})})}catch(P){throw v&&v(),P&&P.name==="TypeError"&&/Load failed|fetch/i.test(P.message)?Object.assign(new q("Network Error",q.ERR_NETWORK,e,y),{cause:P.cause||P}):q.from(P,P&&P.code,e,y)}}),qr={http:wa,xhr:Xa,fetch:oc};_.forEach(qr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const js=e=>`- ${e}`,ic=e=>_.isFunction(e)||e===null||e===!1,li={getAdapter:e=>{e=_.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!ic(n)&&(r=qr[(i=String(n)).toLowerCase()],r===void 0))throw new q(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(js).join(`
`):" "+js(o[0]):"as no adapter specified";throw new q("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:qr};function vr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Xt(null,e)}function $s(e){return vr(e),e.headers=Se.from(e.headers),e.data=xr.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),li.getAdapter(e.adapter||Sn.adapter)(e).then(function(r){return vr(e),r.data=xr.call(e,e.transformResponse,r),r.headers=Se.from(r.headers),r},function(r){return ti(r)||(vr(e),r&&r.response&&(r.response.data=xr.call(e,e.transformResponse,r.response),r.response.headers=Se.from(r.response.headers))),Promise.reject(r)})}const ai="1.10.0",ir={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ir[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Hs={};ir.transitional=function(t,n,r){function s(o,i){return"[Axios v"+ai+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,l)=>{if(t===!1)throw new q(s(i," has been removed"+(n?" in "+n:"")),q.ERR_DEPRECATED);return n&&!Hs[i]&&(Hs[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,l):!0}};ir.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function lc(e,t,n){if(typeof e!="object")throw new q("options must be an object",q.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const l=e[o],a=l===void 0||i(l,o,e);if(a!==!0)throw new q("option "+o+" must be "+a,q.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new q("Unknown option "+o,q.ERR_BAD_OPTION)}}const Mn={assertOptions:lc,validators:ir},Ke=Mn.validators;let At=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Is,response:new Is}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Ct(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&Mn.assertOptions(r,{silentJSONParsing:Ke.transitional(Ke.boolean),forcedJSONParsing:Ke.transitional(Ke.boolean),clarifyTimeoutError:Ke.transitional(Ke.boolean)},!1),s!=null&&(_.isFunction(s)?n.paramsSerializer={serialize:s}:Mn.assertOptions(s,{encode:Ke.function,serialize:Ke.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Mn.assertOptions(n,{baseUrl:Ke.spelling("baseURL"),withXsrfToken:Ke.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&_.merge(o.common,o[n.method]);o&&_.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),n.headers=Se.concat(i,o);const l=[];let a=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(n)===!1||(a=a&&v.synchronous,l.unshift(v.fulfilled,v.rejected))});const u=[];this.interceptors.response.forEach(function(v){u.push(v.fulfilled,v.rejected)});let c,f=0,p;if(!a){const y=[$s.bind(this),void 0];for(y.unshift.apply(y,l),y.push.apply(y,u),p=y.length,c=Promise.resolve(n);f<p;)c=c.then(y[f++],y[f++]);return c}p=l.length;let g=n;for(f=0;f<p;){const y=l[f++],v=l[f++];try{g=y(g)}catch(S){v.call(this,S);break}}try{c=$s.call(this,g)}catch(y){return Promise.reject(y)}for(f=0,p=u.length;f<p;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=Ct(this.defaults,t);const n=ri(t.baseURL,t.url,t.allowAbsoluteUrls);return Qo(n,t.params,t.paramsSerializer)}};_.forEach(["delete","get","head","options"],function(t){At.prototype[t]=function(n,r){return this.request(Ct(r||{},{method:t,url:n,data:(r||{}).data}))}});_.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,l){return this.request(Ct(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}At.prototype[t]=n(),At.prototype[t+"Form"]=n(!0)});let ac=class ci{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(l=>{r.subscribe(l),o=l}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,l){r.reason||(r.reason=new Xt(o,i,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new ci(function(s){t=s}),cancel:t}}};function cc(e){return function(n){return e.apply(null,n)}}function uc(e){return _.isObject(e)&&e.isAxiosError===!0}const Vr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Vr).forEach(([e,t])=>{Vr[t]=e});function ui(e){const t=new At(e),n=jo(At.prototype.request,t);return _.extend(n,At.prototype,t,{allOwnKeys:!0}),_.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return ui(Ct(e,s))},n}const se=ui(Sn);se.Axios=At;se.CanceledError=Xt;se.CancelToken=ac;se.isCancel=ti;se.VERSION=ai;se.toFormData=sr;se.AxiosError=q;se.Cancel=se.CanceledError;se.all=function(t){return Promise.all(t)};se.spread=cc;se.isAxiosError=uc;se.mergeConfig=Ct;se.AxiosHeaders=Se;se.formToJSON=e=>ei(_.isHTMLForm(e)?new FormData(e):e);se.getAdapter=li.getAdapter;se.HttpStatusCode=Vr;se.default=se;const{Axios:oh,AxiosError:ih,CanceledError:lh,isCancel:ah,CancelToken:ch,VERSION:uh,all:fh,Cancel:dh,isAxiosError:hh,spread:ph,toFormData:mh,AxiosHeaders:gh,HttpStatusCode:_h,formToJSON:yh,getAdapter:bh,mergeConfig:wh}=se;window.axios=se;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";window.axios.defaults.baseURL="/api";/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ls(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Z={},Dt=[],Xe=()=>{},fc=()=>!1,lr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),as=e=>e.startsWith("onUpdate:"),me=Object.assign,cs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},dc=Object.prototype.hasOwnProperty,J=(e,t)=>dc.call(e,t),B=Array.isArray,Ut=e=>Rn(e)==="[object Map]",Yt=e=>Rn(e)==="[object Set]",qs=e=>Rn(e)==="[object Date]",$=e=>typeof e=="function",oe=e=>typeof e=="string",De=e=>typeof e=="symbol",re=e=>e!==null&&typeof e=="object",fi=e=>(re(e)||$(e))&&$(e.then)&&$(e.catch),di=Object.prototype.toString,Rn=e=>di.call(e),hc=e=>Rn(e).slice(8,-1),hi=e=>Rn(e)==="[object Object]",us=e=>oe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,on=ls(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ar=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},pc=/-(\w)/g,Ne=ar(e=>e.replace(pc,(t,n)=>n?n.toUpperCase():"")),mc=/\B([A-Z])/g,Ot=ar(e=>e.replace(mc,"-$1").toLowerCase()),cr=ar(e=>e.charAt(0).toUpperCase()+e.slice(1)),Sr=ar(e=>e?`on${cr(e)}`:""),bt=(e,t)=>!Object.is(e,t),Dn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},pi=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Wn=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Vs;const ur=()=>Vs||(Vs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function fs(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=oe(r)?bc(r):fs(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(oe(e)||re(e))return e}const gc=/;(?![^(]*\))/g,_c=/:([^]+)/,yc=/\/\*[^]*?\*\//g;function bc(e){const t={};return e.replace(yc,"").split(gc).forEach(n=>{if(n){const r=n.split(_c);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function kt(e){let t="";if(oe(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const r=kt(e[n]);r&&(t+=r+" ")}else if(re(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const wc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ec=ls(wc);function mi(e){return!!e||e===""}function xc(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=An(e[r],t[r]);return n}function An(e,t){if(e===t)return!0;let n=qs(e),r=qs(t);if(n||r)return n&&r?e.getTime()===t.getTime():!1;if(n=De(e),r=De(t),n||r)return e===t;if(n=B(e),r=B(t),n||r)return n&&r?xc(e,t):!1;if(n=re(e),r=re(t),n||r){if(!n||!r)return!1;const s=Object.keys(e).length,o=Object.keys(t).length;if(s!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!An(e[i],t[i]))return!1}}return String(e)===String(t)}function ds(e,t){return e.findIndex(n=>An(n,t))}const gi=e=>!!(e&&e.__v_isRef===!0),pt=e=>oe(e)?e:e==null?"":B(e)||re(e)&&(e.toString===di||!$(e.toString))?gi(e)?pt(e.value):JSON.stringify(e,_i,2):String(e),_i=(e,t)=>gi(t)?_i(e,t.value):Ut(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Rr(r,o)+" =>"]=s,n),{})}:Yt(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Rr(n))}:De(t)?Rr(t):re(t)&&!B(t)&&!hi(t)?String(t):t,Rr=(e,t="")=>{var n;return De(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let xe;class vc{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=xe,!t&&xe&&(this.index=(xe.scopes||(xe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=xe;try{return xe=this,t()}finally{xe=n}}}on(){++this._on===1&&(this.prevScope=xe,xe=this)}off(){this._on>0&&--this._on===0&&(xe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function Sc(){return xe}let ne;const Ar=new WeakSet;class yi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,xe&&xe.active&&xe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Ar.has(this)&&(Ar.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||wi(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ks(this),Ei(this);const t=ne,n=Me;ne=this,Me=!0;try{return this.fn()}finally{xi(this),ne=t,Me=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ms(t);this.deps=this.depsTail=void 0,Ks(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Ar.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Kr(this)&&this.run()}get dirty(){return Kr(this)}}let bi=0,ln,an;function wi(e,t=!1){if(e.flags|=8,t){e.next=an,an=e;return}e.next=ln,ln=e}function hs(){bi++}function ps(){if(--bi>0)return;if(an){let t=an;for(an=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;ln;){let t=ln;for(ln=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Ei(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function xi(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),ms(r),Rc(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function Kr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(vi(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function vi(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===pn)||(e.globalVersion=pn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Kr(e))))return;e.flags|=2;const t=e.dep,n=ne,r=Me;ne=e,Me=!0;try{Ei(e);const s=e.fn(e._value);(t.version===0||bt(s,e._value))&&(e.flags|=128,e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ne=n,Me=r,xi(e),e.flags&=-3}}function ms(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)ms(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Rc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Me=!0;const Si=[];function lt(){Si.push(Me),Me=!1}function at(){const e=Si.pop();Me=e===void 0?!0:e}function Ks(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ne;ne=void 0;try{t()}finally{ne=n}}}let pn=0;class Ac{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class gs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ne||!Me||ne===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ne)n=this.activeLink=new Ac(ne,this),ne.deps?(n.prevDep=ne.depsTail,ne.depsTail.nextDep=n,ne.depsTail=n):ne.deps=ne.depsTail=n,Ri(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ne.depsTail,n.nextDep=void 0,ne.depsTail.nextDep=n,ne.depsTail=n,ne.deps===n&&(ne.deps=r)}return n}trigger(t){this.version++,pn++,this.notify(t)}notify(t){hs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ps()}}}function Ri(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Ri(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Wr=new WeakMap,Tt=Symbol(""),zr=Symbol(""),mn=Symbol("");function ue(e,t,n){if(Me&&ne){let r=Wr.get(e);r||Wr.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new gs),s.map=r,s.key=n),s.track()}}function rt(e,t,n,r,s,o){const i=Wr.get(e);if(!i){pn++;return}const l=a=>{a&&a.trigger()};if(hs(),t==="clear")i.forEach(l);else{const a=B(e),u=a&&us(n);if(a&&n==="length"){const c=Number(r);i.forEach((f,p)=>{(p==="length"||p===mn||!De(p)&&p>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(mn)),t){case"add":a?u&&l(i.get("length")):(l(i.get(Tt)),Ut(e)&&l(i.get(zr)));break;case"delete":a||(l(i.get(Tt)),Ut(e)&&l(i.get(zr)));break;case"set":Ut(e)&&l(i.get(Tt));break}}ps()}function It(e){const t=G(e);return t===e?t:(ue(t,"iterate",mn),Le(e)?t:t.map(le))}function fr(e){return ue(e=G(e),"iterate",mn),e}const Tc={__proto__:null,[Symbol.iterator](){return Tr(this,Symbol.iterator,le)},concat(...e){return It(this).concat(...e.map(t=>B(t)?It(t):t))},entries(){return Tr(this,"entries",e=>(e[1]=le(e[1]),e))},every(e,t){return Ze(this,"every",e,t,void 0,arguments)},filter(e,t){return Ze(this,"filter",e,t,n=>n.map(le),arguments)},find(e,t){return Ze(this,"find",e,t,le,arguments)},findIndex(e,t){return Ze(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ze(this,"findLast",e,t,le,arguments)},findLastIndex(e,t){return Ze(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ze(this,"forEach",e,t,void 0,arguments)},includes(...e){return Cr(this,"includes",e)},indexOf(...e){return Cr(this,"indexOf",e)},join(e){return It(this).join(e)},lastIndexOf(...e){return Cr(this,"lastIndexOf",e)},map(e,t){return Ze(this,"map",e,t,void 0,arguments)},pop(){return tn(this,"pop")},push(...e){return tn(this,"push",e)},reduce(e,...t){return Ws(this,"reduce",e,t)},reduceRight(e,...t){return Ws(this,"reduceRight",e,t)},shift(){return tn(this,"shift")},some(e,t){return Ze(this,"some",e,t,void 0,arguments)},splice(...e){return tn(this,"splice",e)},toReversed(){return It(this).toReversed()},toSorted(e){return It(this).toSorted(e)},toSpliced(...e){return It(this).toSpliced(...e)},unshift(...e){return tn(this,"unshift",e)},values(){return Tr(this,"values",le)}};function Tr(e,t,n){const r=fr(e),s=r[t]();return r!==e&&!Le(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Cc=Array.prototype;function Ze(e,t,n,r,s,o){const i=fr(e),l=i!==e&&!Le(e),a=i[t];if(a!==Cc[t]){const f=a.apply(e,o);return l?le(f):f}let u=n;i!==e&&(l?u=function(f,p){return n.call(this,le(f),p,e)}:n.length>2&&(u=function(f,p){return n.call(this,f,p,e)}));const c=a.call(i,u,r);return l&&s?s(c):c}function Ws(e,t,n,r){const s=fr(e);let o=n;return s!==e&&(Le(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,le(l),a,e)}),s[t](o,...r)}function Cr(e,t,n){const r=G(e);ue(r,"iterate",mn);const s=r[t](...n);return(s===-1||s===!1)&&bs(n[0])?(n[0]=G(n[0]),r[t](...n)):s}function tn(e,t,n=[]){lt(),hs();const r=G(e)[t].apply(e,n);return ps(),at(),r}const Oc=ls("__proto__,__v_isRef,__isVue"),Ai=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(De));function Pc(e){De(e)||(e=String(e));const t=G(this);return ue(t,"has",e),t.hasOwnProperty(e)}class Ti{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?jc:Li:o?Pi:Oi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=B(t);if(!s){let a;if(i&&(a=Tc[n]))return a;if(n==="hasOwnProperty")return Pc}const l=Reflect.get(t,n,pe(t)?t:r);return(De(n)?Ai.has(n):Oc(n))||(s||ue(t,"get",n),o)?l:pe(l)?i&&us(n)?l:l.value:re(l)?s?Ii(l):dr(l):l}}class Ci extends Ti{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const a=wt(o);if(!Le(r)&&!wt(r)&&(o=G(o),r=G(r)),!B(t)&&pe(o)&&!pe(r))return a?!1:(o.value=r,!0)}const i=B(t)&&us(n)?Number(n)<t.length:J(t,n),l=Reflect.set(t,n,r,pe(t)?t:s);return t===G(s)&&(i?bt(r,o)&&rt(t,"set",n,r):rt(t,"add",n,r)),l}deleteProperty(t,n){const r=J(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&rt(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!De(n)||!Ai.has(n))&&ue(t,"has",n),r}ownKeys(t){return ue(t,"iterate",B(t)?"length":Tt),Reflect.ownKeys(t)}}class Lc extends Ti{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Nc=new Ci,Ic=new Lc,Fc=new Ci(!0);const Gr=e=>e,Pn=e=>Reflect.getPrototypeOf(e);function Mc(e,t,n){return function(...r){const s=this.__v_raw,o=G(s),i=Ut(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=s[e](...r),c=n?Gr:t?zn:le;return!t&&ue(o,"iterate",a?zr:Tt),{next(){const{value:f,done:p}=u.next();return p?{value:f,done:p}:{value:l?[c(f[0]),c(f[1])]:c(f),done:p}},[Symbol.iterator](){return this}}}}function Ln(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Dc(e,t){const n={get(s){const o=this.__v_raw,i=G(o),l=G(s);e||(bt(s,l)&&ue(i,"get",s),ue(i,"get",l));const{has:a}=Pn(i),u=t?Gr:e?zn:le;if(a.call(i,s))return u(o.get(s));if(a.call(i,l))return u(o.get(l));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&ue(G(s),"iterate",Tt),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=G(o),l=G(s);return e||(bt(s,l)&&ue(i,"has",s),ue(i,"has",l)),s===l?o.has(s):o.has(s)||o.has(l)},forEach(s,o){const i=this,l=i.__v_raw,a=G(l),u=t?Gr:e?zn:le;return!e&&ue(a,"iterate",Tt),l.forEach((c,f)=>s.call(o,u(c),u(f),i))}};return me(n,e?{add:Ln("add"),set:Ln("set"),delete:Ln("delete"),clear:Ln("clear")}:{add(s){!t&&!Le(s)&&!wt(s)&&(s=G(s));const o=G(this);return Pn(o).has.call(o,s)||(o.add(s),rt(o,"add",s,s)),this},set(s,o){!t&&!Le(o)&&!wt(o)&&(o=G(o));const i=G(this),{has:l,get:a}=Pn(i);let u=l.call(i,s);u||(s=G(s),u=l.call(i,s));const c=a.call(i,s);return i.set(s,o),u?bt(o,c)&&rt(i,"set",s,o):rt(i,"add",s,o),this},delete(s){const o=G(this),{has:i,get:l}=Pn(o);let a=i.call(o,s);a||(s=G(s),a=i.call(o,s)),l&&l.call(o,s);const u=o.delete(s);return a&&rt(o,"delete",s,void 0),u},clear(){const s=G(this),o=s.size!==0,i=s.clear();return o&&rt(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=Mc(s,e,t)}),n}function _s(e,t){const n=Dc(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(J(n,s)&&s in r?n:r,s,o)}const Uc={get:_s(!1,!1)},kc={get:_s(!1,!0)},Bc={get:_s(!0,!1)};const Oi=new WeakMap,Pi=new WeakMap,Li=new WeakMap,jc=new WeakMap;function $c(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Hc(e){return e.__v_skip||!Object.isExtensible(e)?0:$c(hc(e))}function dr(e){return wt(e)?e:ys(e,!1,Nc,Uc,Oi)}function Ni(e){return ys(e,!1,Fc,kc,Pi)}function Ii(e){return ys(e,!0,Ic,Bc,Li)}function ys(e,t,n,r,s){if(!re(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Hc(e);if(o===0)return e;const i=s.get(e);if(i)return i;const l=new Proxy(e,o===2?r:n);return s.set(e,l),l}function Bt(e){return wt(e)?Bt(e.__v_raw):!!(e&&e.__v_isReactive)}function wt(e){return!!(e&&e.__v_isReadonly)}function Le(e){return!!(e&&e.__v_isShallow)}function bs(e){return e?!!e.__v_raw:!1}function G(e){const t=e&&e.__v_raw;return t?G(t):e}function qc(e){return!J(e,"__v_skip")&&Object.isExtensible(e)&&pi(e,"__v_skip",!0),e}const le=e=>re(e)?dr(e):e,zn=e=>re(e)?Ii(e):e;function pe(e){return e?e.__v_isRef===!0:!1}function Vc(e){return Fi(e,!1)}function Kc(e){return Fi(e,!0)}function Fi(e,t){return pe(e)?e:new Wc(e,t)}class Wc{constructor(t,n){this.dep=new gs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:G(t),this._value=n?t:le(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||Le(t)||wt(t);t=r?t:G(t),bt(t,n)&&(this._rawValue=t,this._value=r?t:le(t),this.dep.trigger())}}function jt(e){return pe(e)?e.value:e}const zc={get:(e,t,n)=>t==="__v_raw"?e:jt(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return pe(s)&&!pe(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function Mi(e){return Bt(e)?e:new Proxy(e,zc)}class Gc{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new gs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=pn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ne!==this)return wi(this,!0),!0}get value(){const t=this.dep.track();return vi(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Jc(e,t,n=!1){let r,s;return $(e)?r=e:(r=e.get,s=e.set),new Gc(r,s,n)}const Nn={},Gn=new WeakMap;let St;function Xc(e,t=!1,n=St){if(n){let r=Gn.get(n);r||Gn.set(n,r=[]),r.push(e)}}function Yc(e,t,n=Z){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:l,call:a}=n,u=F=>s?F:Le(F)||s===!1||s===0?st(F,1):st(F);let c,f,p,g,y=!1,v=!1;if(pe(e)?(f=()=>e.value,y=Le(e)):Bt(e)?(f=()=>u(e),y=!0):B(e)?(v=!0,y=e.some(F=>Bt(F)||Le(F)),f=()=>e.map(F=>{if(pe(F))return F.value;if(Bt(F))return u(F);if($(F))return a?a(F,2):F()})):$(e)?t?f=a?()=>a(e,2):e:f=()=>{if(p){lt();try{p()}finally{at()}}const F=St;St=c;try{return a?a(e,3,[g]):e(g)}finally{St=F}}:f=Xe,t&&s){const F=f,H=s===!0?1/0:s;f=()=>st(F(),H)}const S=Sc(),P=()=>{c.stop(),S&&S.active&&cs(S.effects,c)};if(o&&t){const F=t;t=(...H)=>{F(...H),P()}}let C=v?new Array(e.length).fill(Nn):Nn;const I=F=>{if(!(!(c.flags&1)||!c.dirty&&!F))if(t){const H=c.run();if(s||y||(v?H.some((ee,K)=>bt(ee,C[K])):bt(H,C))){p&&p();const ee=St;St=c;try{const K=[H,C===Nn?void 0:v&&C[0]===Nn?[]:C,g];C=H,a?a(t,3,K):t(...K)}finally{St=ee}}}else c.run()};return l&&l(I),c=new yi(f),c.scheduler=i?()=>i(I,!1):I,g=F=>Xc(F,!1,c),p=c.onStop=()=>{const F=Gn.get(c);if(F){if(a)a(F,4);else for(const H of F)H();Gn.delete(c)}},t?r?I(!0):C=c.run():i?i(I.bind(null,!0),!0):c.run(),P.pause=c.pause.bind(c),P.resume=c.resume.bind(c),P.stop=P,P}function st(e,t=1/0,n){if(t<=0||!re(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,pe(e))st(e.value,t,n);else if(B(e))for(let r=0;r<e.length;r++)st(e[r],t,n);else if(Yt(e)||Ut(e))e.forEach(r=>{st(r,t,n)});else if(hi(e)){for(const r in e)st(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&st(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Tn(e,t,n,r){try{return r?e(...r):e()}catch(s){hr(s,t,n)}}function Ye(e,t,n,r){if($(e)){const s=Tn(e,t,n,r);return s&&fi(s)&&s.catch(o=>{hr(o,t,n)}),s}if(B(e)){const s=[];for(let o=0;o<e.length;o++)s.push(Ye(e[o],t,n,r));return s}}function hr(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Z;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,a,u)===!1)return}l=l.parent}if(o){lt(),Tn(o,null,10,[e,a,u]),at();return}}Qc(e,n,s,r,i)}function Qc(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const be=[];let ze=-1;const $t=[];let mt=null,Ft=0;const Di=Promise.resolve();let Jn=null;function ws(e){const t=Jn||Di;return e?t.then(this?e.bind(this):e):t}function Zc(e){let t=ze+1,n=be.length;for(;t<n;){const r=t+n>>>1,s=be[r],o=gn(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function Es(e){if(!(e.flags&1)){const t=gn(e),n=be[be.length-1];!n||!(e.flags&2)&&t>=gn(n)?be.push(e):be.splice(Zc(t),0,e),e.flags|=1,Ui()}}function Ui(){Jn||(Jn=Di.then(Bi))}function eu(e){B(e)?$t.push(...e):mt&&e.id===-1?mt.splice(Ft+1,0,e):e.flags&1||($t.push(e),e.flags|=1),Ui()}function zs(e,t,n=ze+1){for(;n<be.length;n++){const r=be[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;be.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function ki(e){if($t.length){const t=[...new Set($t)].sort((n,r)=>gn(n)-gn(r));if($t.length=0,mt){mt.push(...t);return}for(mt=t,Ft=0;Ft<mt.length;Ft++){const n=mt[Ft];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}mt=null,Ft=0}}const gn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Bi(e){try{for(ze=0;ze<be.length;ze++){const t=be[ze];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Tn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;ze<be.length;ze++){const t=be[ze];t&&(t.flags&=-2)}ze=-1,be.length=0,ki(),Jn=null,(be.length||$t.length)&&Bi()}}let ae=null,ji=null;function Xn(e){const t=ae;return ae=e,ji=e&&e.type.__scopeId||null,t}function Un(e,t=ae,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&ro(-1);const o=Xn(t);let i;try{i=e(...s)}finally{Xn(o),r._d&&ro(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function Eh(e,t){if(ae===null)return e;const n=_r(ae),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,l,a=Z]=t[s];o&&($(o)&&(o={mounted:o,updated:o}),o.deep&&st(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function xt(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];o&&(l.oldValue=o[i].value);let a=l.dir[r];a&&(lt(),Ye(a,n,8,[e.el,l,e,t]),at())}}const tu=Symbol("_vte"),nu=e=>e.__isTeleport;function xs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,xs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function $i(e,t){return $(e)?me({name:e.name},t,{setup:e}):e}function Hi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Yn(e,t,n,r,s=!1){if(B(e)){e.forEach((y,v)=>Yn(y,t&&(B(t)?t[v]:t),n,r,s));return}if(Ht(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&Yn(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?_r(r.component):r.el,i=s?null:o,{i:l,r:a}=e,u=t&&t.r,c=l.refs===Z?l.refs={}:l.refs,f=l.setupState,p=G(f),g=f===Z?()=>!1:y=>J(p,y);if(u!=null&&u!==a&&(oe(u)?(c[u]=null,g(u)&&(f[u]=null)):pe(u)&&(u.value=null)),$(a))Tn(a,l,12,[i,c]);else{const y=oe(a),v=pe(a);if(y||v){const S=()=>{if(e.f){const P=y?g(a)?f[a]:c[a]:a.value;s?B(P)&&cs(P,o):B(P)?P.includes(o)||P.push(o):y?(c[a]=[o],g(a)&&(f[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else y?(c[a]=i,g(a)&&(f[a]=i)):v&&(a.value=i,e.k&&(c[e.k]=i))};i?(S.id=-1,Te(S,n)):S()}}}ur().requestIdleCallback;ur().cancelIdleCallback;const Ht=e=>!!e.type.__asyncLoader,qi=e=>e.type.__isKeepAlive;function ru(e,t){Vi(e,"a",t)}function su(e,t){Vi(e,"da",t)}function Vi(e,t,n=de){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(pr(t,r,n),n){let s=n.parent;for(;s&&s.parent;)qi(s.parent.vnode)&&ou(r,t,n,s),s=s.parent}}function ou(e,t,n,r){const s=pr(t,e,r,!0);Ki(()=>{cs(r[t],s)},n)}function pr(e,t,n=de,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{lt();const l=Cn(n),a=Ye(t,n,e,i);return l(),at(),a});return r?s.unshift(o):s.push(o),o}}const ut=e=>(t,n=de)=>{(!bn||e==="sp")&&pr(e,(...r)=>t(...r),n)},iu=ut("bm"),lu=ut("m"),au=ut("bu"),cu=ut("u"),uu=ut("bum"),Ki=ut("um"),fu=ut("sp"),du=ut("rtg"),hu=ut("rtc");function pu(e,t=de){pr("ec",e,t)}const Wi="components";function Or(e,t){return Gi(Wi,e,!0,t)||e}const zi=Symbol.for("v-ndc");function xh(e){return oe(e)?Gi(Wi,e,!1)||e:e||zi}function Gi(e,t,n=!0,r=!1){const s=ae||de;if(s){const o=s.type;{const l=ef(o,!1);if(l&&(l===t||l===Ne(t)||l===cr(Ne(t))))return o}const i=Gs(s[e]||o[e],t)||Gs(s.appContext[e],t);return!i&&r?o:i}}function Gs(e,t){return e&&(e[t]||e[Ne(t)]||e[cr(Ne(t))])}function vh(e,t,n,r){let s;const o=n,i=B(e);if(i||oe(e)){const l=i&&Bt(e);let a=!1,u=!1;l&&(a=!Le(e),u=wt(e),e=fr(e)),s=new Array(e.length);for(let c=0,f=e.length;c<f;c++)s[c]=t(a?u?zn(le(e[c])):le(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let l=0;l<e;l++)s[l]=t(l+1,l,void 0,o)}else if(re(e))if(e[Symbol.iterator])s=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);s=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];s[a]=t(e[c],c,a,o)}}else s=[];return s}function Sh(e,t,n={},r,s){if(ae.ce||ae.parent&&Ht(ae.parent)&&ae.parent.ce)return t!=="default"&&(n.name=t),Ge(),Vt(Ce,null,[he("slot",n,r)],64);let o=e[t];o&&o._c&&(o._d=!1),Ge();const i=o&&Ji(o(n)),l=n.key||i&&i.key,a=Vt(Ce,{key:(l&&!De(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),o&&o._c&&(o._d=!0),a}function Ji(e){return e.some(t=>yn(t)?!(t.type===ct||t.type===Ce&&!Ji(t.children)):!0)?e:null}const Jr=e=>e?ml(e)?_r(e):Jr(e.parent):null,cn=me(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Jr(e.parent),$root:e=>Jr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Yi(e),$forceUpdate:e=>e.f||(e.f=()=>{Es(e.update)}),$nextTick:e=>e.n||(e.n=ws.bind(e.proxy)),$watch:e=>Mu.bind(e)}),Pr=(e,t)=>e!==Z&&!e.__isScriptSetup&&J(e,t),mu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(Pr(r,t))return i[t]=1,r[t];if(s!==Z&&J(s,t))return i[t]=2,s[t];if((u=e.propsOptions[0])&&J(u,t))return i[t]=3,o[t];if(n!==Z&&J(n,t))return i[t]=4,n[t];Xr&&(i[t]=0)}}const c=cn[t];let f,p;if(c)return t==="$attrs"&&ue(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==Z&&J(n,t))return i[t]=4,n[t];if(p=a.config.globalProperties,J(p,t))return p[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return Pr(s,t)?(s[t]=n,!0):r!==Z&&J(r,t)?(r[t]=n,!0):J(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let l;return!!n[i]||e!==Z&&J(e,i)||Pr(t,i)||(l=o[0])&&J(l,i)||J(r,i)||J(cn,i)||J(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:J(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Js(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Xr=!0;function gu(e){const t=Yi(e),n=e.proxy,r=e.ctx;Xr=!1,t.beforeCreate&&Xs(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:f,mounted:p,beforeUpdate:g,updated:y,activated:v,deactivated:S,beforeDestroy:P,beforeUnmount:C,destroyed:I,unmounted:F,render:H,renderTracked:ee,renderTriggered:K,errorCaptured:ge,serverPrefetch:Ie,expose:Be,inheritAttrs:ft,components:Et,directives:je,filters:Qt}=t;if(u&&_u(u,r,null),i)for(const Y in i){const W=i[Y];$(W)&&(r[Y]=W.bind(n))}if(s){const Y=s.call(n,n);re(Y)&&(e.data=dr(Y))}if(Xr=!0,o)for(const Y in o){const W=o[Y],Qe=$(W)?W.bind(n,n):$(W.get)?W.get.bind(n,n):Xe,dt=!$(W)&&$(W.set)?W.set.bind(n):Xe,$e=Fe({get:Qe,set:dt});Object.defineProperty(r,Y,{enumerable:!0,configurable:!0,get:()=>$e.value,set:we=>$e.value=we})}if(l)for(const Y in l)Xi(l[Y],r,n,Y);if(a){const Y=$(a)?a.call(n):a;Reflect.ownKeys(Y).forEach(W=>{kn(W,Y[W])})}c&&Xs(c,e,"c");function ie(Y,W){B(W)?W.forEach(Qe=>Y(Qe.bind(n))):W&&Y(W.bind(n))}if(ie(iu,f),ie(lu,p),ie(au,g),ie(cu,y),ie(ru,v),ie(su,S),ie(pu,ge),ie(hu,ee),ie(du,K),ie(uu,C),ie(Ki,F),ie(fu,Ie),B(Be))if(Be.length){const Y=e.exposed||(e.exposed={});Be.forEach(W=>{Object.defineProperty(Y,W,{get:()=>n[W],set:Qe=>n[W]=Qe})})}else e.exposed||(e.exposed={});H&&e.render===Xe&&(e.render=H),ft!=null&&(e.inheritAttrs=ft),Et&&(e.components=Et),je&&(e.directives=je),Ie&&Hi(e)}function _u(e,t,n=Xe){B(e)&&(e=Yr(e));for(const r in e){const s=e[r];let o;re(s)?"default"in s?o=ot(s.from||r,s.default,!0):o=ot(s.from||r):o=ot(s),pe(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function Xs(e,t,n){Ye(B(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Xi(e,t,n,r){let s=r.includes(".")?ul(n,r):()=>n[r];if(oe(e)){const o=t[e];$(o)&&Bn(s,o)}else if($(e))Bn(s,e.bind(n));else if(re(e))if(B(e))e.forEach(o=>Xi(o,t,n,r));else{const o=$(e.handler)?e.handler.bind(n):t[e.handler];$(o)&&Bn(s,o,e)}}function Yi(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!s.length&&!n&&!r?a=t:(a={},s.length&&s.forEach(u=>Qn(a,u,i,!0)),Qn(a,t,i)),re(t)&&o.set(t,a),a}function Qn(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&Qn(e,o,n,!0),s&&s.forEach(i=>Qn(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const l=yu[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const yu={data:Ys,props:Qs,emits:Qs,methods:sn,computed:sn,beforeCreate:_e,created:_e,beforeMount:_e,mounted:_e,beforeUpdate:_e,updated:_e,beforeDestroy:_e,beforeUnmount:_e,destroyed:_e,unmounted:_e,activated:_e,deactivated:_e,errorCaptured:_e,serverPrefetch:_e,components:sn,directives:sn,watch:wu,provide:Ys,inject:bu};function Ys(e,t){return t?e?function(){return me($(e)?e.call(this,this):e,$(t)?t.call(this,this):t)}:t:e}function bu(e,t){return sn(Yr(e),Yr(t))}function Yr(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function _e(e,t){return e?[...new Set([].concat(e,t))]:t}function sn(e,t){return e?me(Object.create(null),e,t):t}function Qs(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:me(Object.create(null),Js(e),Js(t??{})):t}function wu(e,t){if(!e)return t;if(!t)return e;const n=me(Object.create(null),e);for(const r in t)n[r]=_e(e[r],t[r]);return n}function Qi(){return{app:null,config:{isNativeTag:fc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Eu=0;function xu(e,t){return function(r,s=null){$(r)||(r=me({},r)),s!=null&&!re(s)&&(s=null);const o=Qi(),i=new WeakSet,l=[];let a=!1;const u=o.app={_uid:Eu++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:nf,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&$(c.install)?(i.add(c),c.install(u,...f)):$(c)&&(i.add(c),c(u,...f))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,f){return f?(o.components[c]=f,u):o.components[c]},directive(c,f){return f?(o.directives[c]=f,u):o.directives[c]},mount(c,f,p){if(!a){const g=u._ceVNode||he(r,s);return g.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(g,c,p),a=!0,u._container=c,c.__vue_app__=u,_r(g.component)}},onUnmount(c){l.push(c)},unmount(){a&&(Ye(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return o.provides[c]=f,u},runWithContext(c){const f=qt;qt=u;try{return c()}finally{qt=f}}};return u}}let qt=null;function kn(e,t){if(de){let n=de.provides;const r=de.parent&&de.parent.provides;r===n&&(n=de.provides=Object.create(r)),n[e]=t}}function ot(e,t,n=!1){const r=de||ae;if(r||qt){let s=qt?qt._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&$(t)?t.call(r&&r.proxy):t}}const Zi={},el=()=>Object.create(Zi),tl=e=>Object.getPrototypeOf(e)===Zi;function vu(e,t,n,r=!1){const s={},o=el();e.propsDefaults=Object.create(null),nl(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:Ni(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function Su(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,l=G(s),[a]=e.propsOptions;let u=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let p=c[f];if(mr(e.emitsOptions,p))continue;const g=t[p];if(a)if(J(o,p))g!==o[p]&&(o[p]=g,u=!0);else{const y=Ne(p);s[y]=Qr(a,l,y,g,e,!1)}else g!==o[p]&&(o[p]=g,u=!0)}}}else{nl(e,t,s,o)&&(u=!0);let c;for(const f in l)(!t||!J(t,f)&&((c=Ot(f))===f||!J(t,c)))&&(a?n&&(n[f]!==void 0||n[c]!==void 0)&&(s[f]=Qr(a,l,f,void 0,e,!0)):delete s[f]);if(o!==l)for(const f in o)(!t||!J(t,f))&&(delete o[f],u=!0)}u&&rt(e.attrs,"set","")}function nl(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(on(a))continue;const u=t[a];let c;s&&J(s,c=Ne(a))?!o||!o.includes(c)?n[c]=u:(l||(l={}))[c]=u:mr(e.emitsOptions,a)||(!(a in r)||u!==r[a])&&(r[a]=u,i=!0)}if(o){const a=G(n),u=l||Z;for(let c=0;c<o.length;c++){const f=o[c];n[f]=Qr(s,a,f,u[f],e,!J(u,f))}}return i}function Qr(e,t,n,r,s,o){const i=e[n];if(i!=null){const l=J(i,"default");if(l&&r===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&$(a)){const{propsDefaults:u}=s;if(n in u)r=u[n];else{const c=Cn(s);r=u[n]=a.call(null,t),c()}}else r=a;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!l?r=!1:i[1]&&(r===""||r===Ot(n))&&(r=!0))}return r}const Ru=new WeakMap;function rl(e,t,n=!1){const r=n?Ru:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},l=[];let a=!1;if(!$(e)){const c=f=>{a=!0;const[p,g]=rl(f,t,!0);me(i,p),g&&l.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return re(e)&&r.set(e,Dt),Dt;if(B(o))for(let c=0;c<o.length;c++){const f=Ne(o[c]);Zs(f)&&(i[f]=Z)}else if(o)for(const c in o){const f=Ne(c);if(Zs(f)){const p=o[c],g=i[f]=B(p)||$(p)?{type:p}:me({},p),y=g.type;let v=!1,S=!0;if(B(y))for(let P=0;P<y.length;++P){const C=y[P],I=$(C)&&C.name;if(I==="Boolean"){v=!0;break}else I==="String"&&(S=!1)}else v=$(y)&&y.name==="Boolean";g[0]=v,g[1]=S,(v||J(g,"default"))&&l.push(f)}}const u=[i,l];return re(e)&&r.set(e,u),u}function Zs(e){return e[0]!=="$"&&!on(e)}const vs=e=>e[0]==="_"||e==="$stable",Ss=e=>B(e)?e.map(Je):[Je(e)],Au=(e,t,n)=>{if(t._n)return t;const r=Un((...s)=>Ss(t(...s)),n);return r._c=!1,r},sl=(e,t,n)=>{const r=e._ctx;for(const s in e){if(vs(s))continue;const o=e[s];if($(o))t[s]=Au(s,o,r);else if(o!=null){const i=Ss(o);t[s]=()=>i}}},ol=(e,t)=>{const n=Ss(t);e.slots.default=()=>n},il=(e,t,n)=>{for(const r in t)(n||!vs(r))&&(e[r]=t[r])},Tu=(e,t,n)=>{const r=e.slots=el();if(e.vnode.shapeFlag&32){const s=t._;s?(il(r,t,n),n&&pi(r,"_",s,!0)):sl(t,r)}else t&&ol(e,t)},Cu=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=Z;if(r.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:il(s,t,n):(o=!t.$stable,sl(t,s)),i=t}else t&&(ol(e,t),i={default:1});if(o)for(const l in s)!vs(l)&&i[l]==null&&delete s[l]},Te=Hu;function Ou(e){return Pu(e)}function Pu(e,t){const n=ur();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:f,nextSibling:p,setScopeId:g=Xe,insertStaticContent:y}=e,v=(d,h,m,b=null,x=null,E=null,O=void 0,T=null,A=!!h.dynamicChildren)=>{if(d===h)return;d&&!nn(d,h)&&(b=w(d),we(d,x,E,!0),d=null),h.patchFlag===-2&&(A=!1,h.dynamicChildren=null);const{type:R,ref:k,shapeFlag:N}=h;switch(R){case gr:S(d,h,m,b);break;case ct:P(d,h,m,b);break;case Nr:d==null&&C(h,m,b,O);break;case Ce:Et(d,h,m,b,x,E,O,T,A);break;default:N&1?H(d,h,m,b,x,E,O,T,A):N&6?je(d,h,m,b,x,E,O,T,A):(N&64||N&128)&&R.process(d,h,m,b,x,E,O,T,A,D)}k!=null&&x&&Yn(k,d&&d.ref,E,h||d,!h)},S=(d,h,m,b)=>{if(d==null)r(h.el=l(h.children),m,b);else{const x=h.el=d.el;h.children!==d.children&&u(x,h.children)}},P=(d,h,m,b)=>{d==null?r(h.el=a(h.children||""),m,b):h.el=d.el},C=(d,h,m,b)=>{[d.el,d.anchor]=y(d.children,h,m,b,d.el,d.anchor)},I=({el:d,anchor:h},m,b)=>{let x;for(;d&&d!==h;)x=p(d),r(d,m,b),d=x;r(h,m,b)},F=({el:d,anchor:h})=>{let m;for(;d&&d!==h;)m=p(d),s(d),d=m;s(h)},H=(d,h,m,b,x,E,O,T,A)=>{h.type==="svg"?O="svg":h.type==="math"&&(O="mathml"),d==null?ee(h,m,b,x,E,O,T,A):Ie(d,h,x,E,O,T,A)},ee=(d,h,m,b,x,E,O,T)=>{let A,R;const{props:k,shapeFlag:N,transition:U,dirs:j}=d;if(A=d.el=i(d.type,E,k&&k.is,k),N&8?c(A,d.children):N&16&&ge(d.children,A,null,b,x,Lr(d,E),O,T),j&&xt(d,null,b,"created"),K(A,d,d.scopeId,O,b),k){for(const te in k)te!=="value"&&!on(te)&&o(A,te,null,k[te],E,b);"value"in k&&o(A,"value",null,k.value,E),(R=k.onVnodeBeforeMount)&&We(R,b,d)}j&&xt(d,null,b,"beforeMount");const V=Lu(x,U);V&&U.beforeEnter(A),r(A,h,m),((R=k&&k.onVnodeMounted)||V||j)&&Te(()=>{R&&We(R,b,d),V&&U.enter(A),j&&xt(d,null,b,"mounted")},x)},K=(d,h,m,b,x)=>{if(m&&g(d,m),b)for(let E=0;E<b.length;E++)g(d,b[E]);if(x){let E=x.subTree;if(h===E||dl(E.type)&&(E.ssContent===h||E.ssFallback===h)){const O=x.vnode;K(d,O,O.scopeId,O.slotScopeIds,x.parent)}}},ge=(d,h,m,b,x,E,O,T,A=0)=>{for(let R=A;R<d.length;R++){const k=d[R]=T?gt(d[R]):Je(d[R]);v(null,k,h,m,b,x,E,O,T)}},Ie=(d,h,m,b,x,E,O)=>{const T=h.el=d.el;let{patchFlag:A,dynamicChildren:R,dirs:k}=h;A|=d.patchFlag&16;const N=d.props||Z,U=h.props||Z;let j;if(m&&vt(m,!1),(j=U.onVnodeBeforeUpdate)&&We(j,m,h,d),k&&xt(h,d,m,"beforeUpdate"),m&&vt(m,!0),(N.innerHTML&&U.innerHTML==null||N.textContent&&U.textContent==null)&&c(T,""),R?Be(d.dynamicChildren,R,T,m,b,Lr(h,x),E):O||W(d,h,T,null,m,b,Lr(h,x),E,!1),A>0){if(A&16)ft(T,N,U,m,x);else if(A&2&&N.class!==U.class&&o(T,"class",null,U.class,x),A&4&&o(T,"style",N.style,U.style,x),A&8){const V=h.dynamicProps;for(let te=0;te<V.length;te++){const X=V[te],Re=N[X],Ee=U[X];(Ee!==Re||X==="value")&&o(T,X,Re,Ee,x,m)}}A&1&&d.children!==h.children&&c(T,h.children)}else!O&&R==null&&ft(T,N,U,m,x);((j=U.onVnodeUpdated)||k)&&Te(()=>{j&&We(j,m,h,d),k&&xt(h,d,m,"updated")},b)},Be=(d,h,m,b,x,E,O)=>{for(let T=0;T<h.length;T++){const A=d[T],R=h[T],k=A.el&&(A.type===Ce||!nn(A,R)||A.shapeFlag&198)?f(A.el):m;v(A,R,k,null,b,x,E,O,!0)}},ft=(d,h,m,b,x)=>{if(h!==m){if(h!==Z)for(const E in h)!on(E)&&!(E in m)&&o(d,E,h[E],null,x,b);for(const E in m){if(on(E))continue;const O=m[E],T=h[E];O!==T&&E!=="value"&&o(d,E,T,O,x,b)}"value"in m&&o(d,"value",h.value,m.value,x)}},Et=(d,h,m,b,x,E,O,T,A)=>{const R=h.el=d?d.el:l(""),k=h.anchor=d?d.anchor:l("");let{patchFlag:N,dynamicChildren:U,slotScopeIds:j}=h;j&&(T=T?T.concat(j):j),d==null?(r(R,m,b),r(k,m,b),ge(h.children||[],m,k,x,E,O,T,A)):N>0&&N&64&&U&&d.dynamicChildren?(Be(d.dynamicChildren,U,m,x,E,O,T),(h.key!=null||x&&h===x.subTree)&&ll(d,h,!0)):W(d,h,m,k,x,E,O,T,A)},je=(d,h,m,b,x,E,O,T,A)=>{h.slotScopeIds=T,d==null?h.shapeFlag&512?x.ctx.activate(h,m,b,O,A):Qt(h,m,b,x,E,O,A):Pt(d,h,A)},Qt=(d,h,m,b,x,E,O)=>{const T=d.component=Ju(d,b,x);if(qi(d)&&(T.ctx.renderer=D),Xu(T,!1,O),T.asyncDep){if(x&&x.registerDep(T,ie,O),!d.el){const A=T.subTree=he(ct);P(null,A,h,m)}}else ie(T,d,h,m,x,E,O)},Pt=(d,h,m)=>{const b=h.component=d.component;if(ju(d,h,m))if(b.asyncDep&&!b.asyncResolved){Y(b,h,m);return}else b.next=h,b.update();else h.el=d.el,b.vnode=h},ie=(d,h,m,b,x,E,O)=>{const T=()=>{if(d.isMounted){let{next:N,bu:U,u:j,parent:V,vnode:te}=d;{const qe=al(d);if(qe){N&&(N.el=te.el,Y(d,N,O)),qe.asyncDep.then(()=>{d.isUnmounted||T()});return}}let X=N,Re;vt(d,!1),N?(N.el=te.el,Y(d,N,O)):N=te,U&&Dn(U),(Re=N.props&&N.props.onVnodeBeforeUpdate)&&We(Re,V,N,te),vt(d,!0);const Ee=to(d),He=d.subTree;d.subTree=Ee,v(He,Ee,f(He.el),w(He),d,x,E),N.el=Ee.el,X===null&&$u(d,Ee.el),j&&Te(j,x),(Re=N.props&&N.props.onVnodeUpdated)&&Te(()=>We(Re,V,N,te),x)}else{let N;const{el:U,props:j}=h,{bm:V,m:te,parent:X,root:Re,type:Ee}=d,He=Ht(h);vt(d,!1),V&&Dn(V),!He&&(N=j&&j.onVnodeBeforeMount)&&We(N,X,h),vt(d,!0);{Re.ce&&Re.ce._injectChildStyle(Ee);const qe=d.subTree=to(d);v(null,qe,m,b,d,x,E),h.el=qe.el}if(te&&Te(te,x),!He&&(N=j&&j.onVnodeMounted)){const qe=h;Te(()=>We(N,X,qe),x)}(h.shapeFlag&256||X&&Ht(X.vnode)&&X.vnode.shapeFlag&256)&&d.a&&Te(d.a,x),d.isMounted=!0,h=m=b=null}};d.scope.on();const A=d.effect=new yi(T);d.scope.off();const R=d.update=A.run.bind(A),k=d.job=A.runIfDirty.bind(A);k.i=d,k.id=d.uid,A.scheduler=()=>Es(k),vt(d,!0),R()},Y=(d,h,m)=>{h.component=d;const b=d.vnode.props;d.vnode=h,d.next=null,Su(d,h.props,b,m),Cu(d,h.children,m),lt(),zs(d),at()},W=(d,h,m,b,x,E,O,T,A=!1)=>{const R=d&&d.children,k=d?d.shapeFlag:0,N=h.children,{patchFlag:U,shapeFlag:j}=h;if(U>0){if(U&128){dt(R,N,m,b,x,E,O,T,A);return}else if(U&256){Qe(R,N,m,b,x,E,O,T,A);return}}j&8?(k&16&&Pe(R,x,E),N!==R&&c(m,N)):k&16?j&16?dt(R,N,m,b,x,E,O,T,A):Pe(R,x,E,!0):(k&8&&c(m,""),j&16&&ge(N,m,b,x,E,O,T,A))},Qe=(d,h,m,b,x,E,O,T,A)=>{d=d||Dt,h=h||Dt;const R=d.length,k=h.length,N=Math.min(R,k);let U;for(U=0;U<N;U++){const j=h[U]=A?gt(h[U]):Je(h[U]);v(d[U],j,m,null,x,E,O,T,A)}R>k?Pe(d,x,E,!0,!1,N):ge(h,m,b,x,E,O,T,A,N)},dt=(d,h,m,b,x,E,O,T,A)=>{let R=0;const k=h.length;let N=d.length-1,U=k-1;for(;R<=N&&R<=U;){const j=d[R],V=h[R]=A?gt(h[R]):Je(h[R]);if(nn(j,V))v(j,V,m,null,x,E,O,T,A);else break;R++}for(;R<=N&&R<=U;){const j=d[N],V=h[U]=A?gt(h[U]):Je(h[U]);if(nn(j,V))v(j,V,m,null,x,E,O,T,A);else break;N--,U--}if(R>N){if(R<=U){const j=U+1,V=j<k?h[j].el:b;for(;R<=U;)v(null,h[R]=A?gt(h[R]):Je(h[R]),m,V,x,E,O,T,A),R++}}else if(R>U)for(;R<=N;)we(d[R],x,E,!0),R++;else{const j=R,V=R,te=new Map;for(R=V;R<=U;R++){const Ae=h[R]=A?gt(h[R]):Je(h[R]);Ae.key!=null&&te.set(Ae.key,R)}let X,Re=0;const Ee=U-V+1;let He=!1,qe=0;const Zt=new Array(Ee);for(R=0;R<Ee;R++)Zt[R]=0;for(R=j;R<=N;R++){const Ae=d[R];if(Re>=Ee){we(Ae,x,E,!0);continue}let Ve;if(Ae.key!=null)Ve=te.get(Ae.key);else for(X=V;X<=U;X++)if(Zt[X-V]===0&&nn(Ae,h[X])){Ve=X;break}Ve===void 0?we(Ae,x,E,!0):(Zt[Ve-V]=R+1,Ve>=qe?qe=Ve:He=!0,v(Ae,h[Ve],m,null,x,E,O,T,A),Re++)}const Cs=He?Nu(Zt):Dt;for(X=Cs.length-1,R=Ee-1;R>=0;R--){const Ae=V+R,Ve=h[Ae],Os=Ae+1<k?h[Ae+1].el:b;Zt[R]===0?v(null,Ve,m,Os,x,E,O,T,A):He&&(X<0||R!==Cs[X]?$e(Ve,m,Os,2):X--)}}},$e=(d,h,m,b,x=null)=>{const{el:E,type:O,transition:T,children:A,shapeFlag:R}=d;if(R&6){$e(d.component.subTree,h,m,b);return}if(R&128){d.suspense.move(h,m,b);return}if(R&64){O.move(d,h,m,D);return}if(O===Ce){r(E,h,m);for(let N=0;N<A.length;N++)$e(A[N],h,m,b);r(d.anchor,h,m);return}if(O===Nr){I(d,h,m);return}if(b!==2&&R&1&&T)if(b===0)T.beforeEnter(E),r(E,h,m),Te(()=>T.enter(E),x);else{const{leave:N,delayLeave:U,afterLeave:j}=T,V=()=>{d.ctx.isUnmounted?s(E):r(E,h,m)},te=()=>{N(E,()=>{V(),j&&j()})};U?U(E,V,te):te()}else r(E,h,m)},we=(d,h,m,b=!1,x=!1)=>{const{type:E,props:O,ref:T,children:A,dynamicChildren:R,shapeFlag:k,patchFlag:N,dirs:U,cacheIndex:j}=d;if(N===-2&&(x=!1),T!=null&&(lt(),Yn(T,null,m,d,!0),at()),j!=null&&(h.renderCache[j]=void 0),k&256){h.ctx.deactivate(d);return}const V=k&1&&U,te=!Ht(d);let X;if(te&&(X=O&&O.onVnodeBeforeUnmount)&&We(X,h,d),k&6)On(d.component,m,b);else{if(k&128){d.suspense.unmount(m,b);return}V&&xt(d,null,h,"beforeUnmount"),k&64?d.type.remove(d,h,m,D,b):R&&!R.hasOnce&&(E!==Ce||N>0&&N&64)?Pe(R,h,m,!1,!0):(E===Ce&&N&384||!x&&k&16)&&Pe(A,h,m),b&&Lt(d)}(te&&(X=O&&O.onVnodeUnmounted)||V)&&Te(()=>{X&&We(X,h,d),V&&xt(d,null,h,"unmounted")},m)},Lt=d=>{const{type:h,el:m,anchor:b,transition:x}=d;if(h===Ce){Nt(m,b);return}if(h===Nr){F(d);return}const E=()=>{s(m),x&&!x.persisted&&x.afterLeave&&x.afterLeave()};if(d.shapeFlag&1&&x&&!x.persisted){const{leave:O,delayLeave:T}=x,A=()=>O(m,E);T?T(d.el,E,A):A()}else E()},Nt=(d,h)=>{let m;for(;d!==h;)m=p(d),s(d),d=m;s(h)},On=(d,h,m)=>{const{bum:b,scope:x,job:E,subTree:O,um:T,m:A,a:R,parent:k,slots:{__:N}}=d;eo(A),eo(R),b&&Dn(b),k&&B(N)&&N.forEach(U=>{k.renderCache[U]=void 0}),x.stop(),E&&(E.flags|=8,we(O,d,h,m)),T&&Te(T,h),Te(()=>{d.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Pe=(d,h,m,b=!1,x=!1,E=0)=>{for(let O=E;O<d.length;O++)we(d[O],h,m,b,x)},w=d=>{if(d.shapeFlag&6)return w(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const h=p(d.anchor||d.el),m=h&&h[tu];return m?p(m):h};let M=!1;const L=(d,h,m)=>{d==null?h._vnode&&we(h._vnode,null,null,!0):v(h._vnode||null,d,h,null,null,null,m),h._vnode=d,M||(M=!0,zs(),ki(),M=!1)},D={p:v,um:we,m:$e,r:Lt,mt:Qt,mc:ge,pc:W,pbc:Be,n:w,o:e};return{render:L,hydrate:void 0,createApp:xu(L)}}function Lr({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function vt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Lu(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ll(e,t,n=!1){const r=e.children,s=t.children;if(B(r)&&B(s))for(let o=0;o<r.length;o++){const i=r[o];let l=s[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[o]=gt(s[o]),l.el=i.el),!n&&l.patchFlag!==-2&&ll(i,l)),l.type===gr&&(l.el=i.el),l.type===ct&&!l.el&&(l.el=i.el)}}function Nu(e){const t=e.slice(),n=[0];let r,s,o,i,l;const a=e.length;for(r=0;r<a;r++){const u=e[r];if(u!==0){if(s=n[n.length-1],e[s]<u){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<u?o=l+1:i=l;u<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function al(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:al(t)}function eo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Iu=Symbol.for("v-scx"),Fu=()=>ot(Iu);function Bn(e,t,n){return cl(e,t,n)}function cl(e,t,n=Z){const{immediate:r,deep:s,flush:o,once:i}=n,l=me({},n),a=t&&r||!t&&o!=="post";let u;if(bn){if(o==="sync"){const g=Fu();u=g.__watcherHandles||(g.__watcherHandles=[])}else if(!a){const g=()=>{};return g.stop=Xe,g.resume=Xe,g.pause=Xe,g}}const c=de;l.call=(g,y,v)=>Ye(g,c,y,v);let f=!1;o==="post"?l.scheduler=g=>{Te(g,c&&c.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(g,y)=>{y?g():Es(g)}),l.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,c&&(g.id=c.uid,g.i=c))};const p=Yc(e,t,l);return bn&&(u?u.push(p):a&&p()),p}function Mu(e,t,n){const r=this.proxy,s=oe(e)?e.includes(".")?ul(r,e):()=>r[e]:e.bind(r,r);let o;$(t)?o=t:(o=t.handler,n=t);const i=Cn(this),l=cl(s,o.bind(r),n);return i(),l}function ul(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const Du=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ne(t)}Modifiers`]||e[`${Ot(t)}Modifiers`];function Uu(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Z;let s=n;const o=t.startsWith("update:"),i=o&&Du(r,t.slice(7));i&&(i.trim&&(s=n.map(c=>oe(c)?c.trim():c)),i.number&&(s=n.map(Wn)));let l,a=r[l=Sr(t)]||r[l=Sr(Ne(t))];!a&&o&&(a=r[l=Sr(Ot(t))]),a&&Ye(a,e,6,s);const u=r[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ye(u,e,6,s)}}function fl(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},l=!1;if(!$(e)){const a=u=>{const c=fl(u,t,!0);c&&(l=!0,me(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(re(e)&&r.set(e,null),null):(B(o)?o.forEach(a=>i[a]=null):me(i,o),re(e)&&r.set(e,i),i)}function mr(e,t){return!e||!lr(t)?!1:(t=t.slice(2).replace(/Once$/,""),J(e,t[0].toLowerCase()+t.slice(1))||J(e,Ot(t))||J(e,t))}function to(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:f,data:p,setupState:g,ctx:y,inheritAttrs:v}=e,S=Xn(e);let P,C;try{if(n.shapeFlag&4){const F=s||r,H=F;P=Je(u.call(H,F,c,f,g,p,y)),C=l}else{const F=t;P=Je(F.length>1?F(f,{attrs:l,slots:i,emit:a}):F(f,null)),C=t.props?l:ku(l)}}catch(F){un.length=0,hr(F,e,1),P=he(ct)}let I=P;if(C&&v!==!1){const F=Object.keys(C),{shapeFlag:H}=I;F.length&&H&7&&(o&&F.some(as)&&(C=Bu(C,o)),I=Kt(I,C,!1,!0))}return n.dirs&&(I=Kt(I,null,!1,!0),I.dirs=I.dirs?I.dirs.concat(n.dirs):n.dirs),n.transition&&xs(I,n.transition),P=I,Xn(S),P}const ku=e=>{let t;for(const n in e)(n==="class"||n==="style"||lr(n))&&((t||(t={}))[n]=e[n]);return t},Bu=(e,t)=>{const n={};for(const r in e)(!as(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function ju(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:l,patchFlag:a}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?no(r,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const p=c[f];if(i[p]!==r[p]&&!mr(u,p))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===i?!1:r?i?no(r,i,u):!0:!!i;return!1}function no(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!mr(n,o))return!0}return!1}function $u({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const dl=e=>e.__isSuspense;function Hu(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):eu(e)}const Ce=Symbol.for("v-fgt"),gr=Symbol.for("v-txt"),ct=Symbol.for("v-cmt"),Nr=Symbol.for("v-stc"),un=[];let Oe=null;function Ge(e=!1){un.push(Oe=e?null:[])}function qu(){un.pop(),Oe=un[un.length-1]||null}let _n=1;function ro(e,t=!1){_n+=e,e<0&&Oe&&t&&(Oe.hasOnce=!0)}function hl(e){return e.dynamicChildren=_n>0?Oe||Dt:null,qu(),_n>0&&Oe&&Oe.push(e),e}function jn(e,t,n,r,s,o){return hl(ye(e,t,n,r,s,o,!0))}function Vt(e,t,n,r,s){return hl(he(e,t,n,r,s,!0))}function yn(e){return e?e.__v_isVNode===!0:!1}function nn(e,t){return e.type===t.type&&e.key===t.key}const pl=({key:e})=>e??null,$n=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?oe(e)||pe(e)||$(e)?{i:ae,r:e,k:t,f:!!n}:e:null);function ye(e,t=null,n=null,r=0,s=null,o=e===Ce?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&pl(t),ref:t&&$n(t),scopeId:ji,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:ae};return l?(Rs(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=oe(n)?8:16),_n>0&&!i&&Oe&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&Oe.push(a),a}const he=Vu;function Vu(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===zi)&&(e=ct),yn(e)){const l=Kt(e,t,!0);return n&&Rs(l,n),_n>0&&!o&&Oe&&(l.shapeFlag&6?Oe[Oe.indexOf(e)]=l:Oe.push(l)),l.patchFlag=-2,l}if(tf(e)&&(e=e.__vccOpts),t){t=Ku(t);let{class:l,style:a}=t;l&&!oe(l)&&(t.class=kt(l)),re(a)&&(bs(a)&&!B(a)&&(a=me({},a)),t.style=fs(a))}const i=oe(e)?1:dl(e)?128:nu(e)?64:re(e)?4:$(e)?2:0;return ye(e,t,n,r,s,i,o,!0)}function Ku(e){return e?bs(e)||tl(e)?me({},e):e:null}function Kt(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:l,transition:a}=e,u=t?Wu(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&pl(u),ref:t&&t.ref?n&&o?B(o)?o.concat($n(t)):[o,$n(t)]:$n(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ce?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Kt(e.ssContent),ssFallback:e.ssFallback&&Kt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&r&&xs(c,a.clone(c)),c}function Hn(e=" ",t=0){return he(gr,null,e,t)}function Ir(e="",t=!1){return t?(Ge(),Vt(ct,null,e)):he(ct,null,e)}function Je(e){return e==null||typeof e=="boolean"?he(ct):B(e)?he(Ce,null,e.slice()):yn(e)?gt(e):he(gr,null,String(e))}function gt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Kt(e)}function Rs(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),Rs(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!tl(t)?t._ctx=ae:s===3&&ae&&(ae.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else $(t)?(t={default:t,_ctx:ae},n=32):(t=String(t),r&64?(n=16,t=[Hn(t)]):n=8);e.children=t,e.shapeFlag|=n}function Wu(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=kt([t.class,r.class]));else if(s==="style")t.style=fs([t.style,r.style]);else if(lr(s)){const o=t[s],i=r[s];i&&o!==i&&!(B(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function We(e,t,n,r=null){Ye(e,t,7,[n,r])}const zu=Qi();let Gu=0;function Ju(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||zu,o={uid:Gu++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new vc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:rl(r,s),emitsOptions:fl(r,s),emit:null,emitted:null,propsDefaults:Z,inheritAttrs:r.inheritAttrs,ctx:Z,data:Z,props:Z,attrs:Z,slots:Z,refs:Z,setupState:Z,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Uu.bind(null,o),e.ce&&e.ce(o),o}let de=null,Zn,Zr;{const e=ur(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};Zn=t("__VUE_INSTANCE_SETTERS__",n=>de=n),Zr=t("__VUE_SSR_SETTERS__",n=>bn=n)}const Cn=e=>{const t=de;return Zn(e),e.scope.on(),()=>{e.scope.off(),Zn(t)}},so=()=>{de&&de.scope.off(),Zn(null)};function ml(e){return e.vnode.shapeFlag&4}let bn=!1;function Xu(e,t=!1,n=!1){t&&Zr(t);const{props:r,children:s}=e.vnode,o=ml(e);vu(e,r,o,t),Tu(e,s,n||t);const i=o?Yu(e,t):void 0;return t&&Zr(!1),i}function Yu(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,mu);const{setup:r}=n;if(r){lt();const s=e.setupContext=r.length>1?Zu(e):null,o=Cn(e),i=Tn(r,e,0,[e.props,s]),l=fi(i);if(at(),o(),(l||e.sp)&&!Ht(e)&&Hi(e),l){if(i.then(so,so),t)return i.then(a=>{oo(e,a)}).catch(a=>{hr(a,e,0)});e.asyncDep=i}else oo(e,i)}else gl(e)}function oo(e,t,n){$(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:re(t)&&(e.setupState=Mi(t)),gl(e)}function gl(e,t,n){const r=e.type;e.render||(e.render=r.render||Xe);{const s=Cn(e);lt();try{gu(e)}finally{at(),s()}}}const Qu={get(e,t){return ue(e,"get",""),e[t]}};function Zu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Qu),slots:e.slots,emit:e.emit,expose:t}}function _r(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Mi(qc(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in cn)return cn[n](e)},has(t,n){return n in t||n in cn}})):e.proxy}function ef(e,t=!0){return $(e)?e.displayName||e.name:e.name||t&&e.__name}function tf(e){return $(e)&&"__vccOpts"in e}const Fe=(e,t)=>Jc(e,t,bn);function _l(e,t,n){const r=arguments.length;return r===2?re(t)&&!B(t)?yn(t)?he(e,null,[t]):he(e,t):he(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&yn(n)&&(n=[n]),he(e,t,n))}const nf="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let es;const io=typeof window<"u"&&window.trustedTypes;if(io)try{es=io.createPolicy("vue",{createHTML:e=>e})}catch{}const yl=es?e=>es.createHTML(e):e=>e,rf="http://www.w3.org/2000/svg",sf="http://www.w3.org/1998/Math/MathML",tt=typeof document<"u"?document:null,lo=tt&&tt.createElement("template"),of={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?tt.createElementNS(rf,e):t==="mathml"?tt.createElementNS(sf,e):n?tt.createElement(e,{is:n}):tt.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>tt.createTextNode(e),createComment:e=>tt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>tt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{lo.innerHTML=yl(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=lo.content;if(r==="svg"||r==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},lf=Symbol("_vtc");function af(e,t,n){const r=e[lf];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ao=Symbol("_vod"),cf=Symbol("_vsh"),uf=Symbol(""),ff=/(^|;)\s*display\s*:/;function df(e,t,n){const r=e.style,s=oe(n);let o=!1;if(n&&!s){if(t)if(oe(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&qn(r,l,"")}else for(const i in t)n[i]==null&&qn(r,i,"");for(const i in n)i==="display"&&(o=!0),qn(r,i,n[i])}else if(s){if(t!==n){const i=r[uf];i&&(n+=";"+i),r.cssText=n,o=ff.test(n)}}else t&&e.removeAttribute("style");ao in e&&(e[ao]=o?r.display:"",e[cf]&&(r.display="none"))}const co=/\s*!important$/;function qn(e,t,n){if(B(n))n.forEach(r=>qn(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=hf(e,t);co.test(n)?e.setProperty(Ot(r),n.replace(co,""),"important"):e[r]=n}}const uo=["Webkit","Moz","ms"],Fr={};function hf(e,t){const n=Fr[t];if(n)return n;let r=Ne(t);if(r!=="filter"&&r in e)return Fr[t]=r;r=cr(r);for(let s=0;s<uo.length;s++){const o=uo[s]+r;if(o in e)return Fr[t]=o}return t}const fo="http://www.w3.org/1999/xlink";function ho(e,t,n,r,s,o=Ec(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(fo,t.slice(6,t.length)):e.setAttributeNS(fo,t,n):n==null||o&&!mi(n)?e.removeAttribute(t):e.setAttribute(t,o?"":De(n)?String(n):n)}function po(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?yl(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=mi(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function yt(e,t,n,r){e.addEventListener(t,n,r)}function pf(e,t,n,r){e.removeEventListener(t,n,r)}const mo=Symbol("_vei");function mf(e,t,n,r,s=null){const o=e[mo]||(e[mo]={}),i=o[t];if(r&&i)i.value=r;else{const[l,a]=gf(t);if(r){const u=o[t]=bf(r,s);yt(e,l,u,a)}else i&&(pf(e,l,i,a),o[t]=void 0)}}const go=/(?:Once|Passive|Capture)$/;function gf(e){let t;if(go.test(e)){t={};let r;for(;r=e.match(go);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Ot(e.slice(2)),t]}let Mr=0;const _f=Promise.resolve(),yf=()=>Mr||(_f.then(()=>Mr=0),Mr=Date.now());function bf(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Ye(wf(r,n.value),t,5,[r])};return n.value=e,n.attached=yf(),n}function wf(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const _o=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ef=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?af(e,r,i):t==="style"?df(e,n,r):lr(t)?as(t)||mf(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):xf(e,t,r,i))?(po(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ho(e,t,r,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!oe(r))?po(e,Ne(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),ho(e,t,r,i))};function xf(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&_o(t)&&$(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return _o(t)&&oe(n)?!1:t in e}const Wt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>Dn(t,n):t};function vf(e){e.target.composing=!0}function yo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const it=Symbol("_assign"),Rh={created(e,{modifiers:{lazy:t,trim:n,number:r}},s){e[it]=Wt(s);const o=r||s.props&&s.props.type==="number";yt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=Wn(l)),e[it](l)}),n&&yt(e,"change",()=>{e.value=e.value.trim()}),t||(yt(e,"compositionstart",vf),yt(e,"compositionend",yo),yt(e,"change",yo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:s,number:o}},i){if(e[it]=Wt(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Wn(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||s&&e.value.trim()===a)||(e.value=a))}},Ah={deep:!0,created(e,t,n){e[it]=Wt(n),yt(e,"change",()=>{const r=e._modelValue,s=wn(e),o=e.checked,i=e[it];if(B(r)){const l=ds(r,s),a=l!==-1;if(o&&!a)i(r.concat(s));else if(!o&&a){const u=[...r];u.splice(l,1),i(u)}}else if(Yt(r)){const l=new Set(r);o?l.add(s):l.delete(s),i(l)}else i(bl(e,o))})},mounted:bo,beforeUpdate(e,t,n){e[it]=Wt(n),bo(e,t,n)}};function bo(e,{value:t,oldValue:n},r){e._modelValue=t;let s;if(B(t))s=ds(t,r.props.value)>-1;else if(Yt(t))s=t.has(r.props.value);else{if(t===n)return;s=An(t,bl(e,!0))}e.checked!==s&&(e.checked=s)}const Th={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const s=Yt(t);yt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?Wn(wn(i)):wn(i));e[it](e.multiple?s?new Set(o):o:o[0]),e._assigning=!0,ws(()=>{e._assigning=!1})}),e[it]=Wt(r)},mounted(e,{value:t}){wo(e,t)},beforeUpdate(e,t,n){e[it]=Wt(n)},updated(e,{value:t}){e._assigning||wo(e,t)}};function wo(e,t){const n=e.multiple,r=B(t);if(!(n&&!r&&!Yt(t))){for(let s=0,o=e.options.length;s<o;s++){const i=e.options[s],l=wn(i);if(n)if(r){const a=typeof l;a==="string"||a==="number"?i.selected=t.some(u=>String(u)===String(l)):i.selected=ds(t,l)>-1}else i.selected=t.has(l);else if(An(wn(i),t)){e.selectedIndex!==s&&(e.selectedIndex=s);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function wn(e){return"_value"in e?e._value:e.value}function bl(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Sf=["ctrl","shift","alt","meta"],Rf={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Sf.some(n=>e[`${n}Key`]&&!t.includes(n))},Ch=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const l=Rf[t[i]];if(l&&l(s,t))return}return e(s,...o)})},Af=me({patchProp:Ef},of);let Eo;function Tf(){return Eo||(Eo=Ou(Af))}const Cf=(...e)=>{const t=Tf().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=Pf(r);if(!s)return;const o=t._component;!$(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,Of(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function Of(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Pf(e){return oe(e)?document.querySelector(e):e}const wl={data(){return{localeUpdateKey:0}},mounted(){window.addEventListener("localeChanged",this.handleLocaleChange),window.addEventListener("forceUpdate",this.handleForceUpdate)},beforeUnmount(){window.removeEventListener("localeChanged",this.handleLocaleChange),window.removeEventListener("forceUpdate",this.handleForceUpdate)},methods:{handleLocaleChange(){this.localeUpdateKey++,this.$forceUpdate(),typeof this.initializeColumns=="function"&&this.initializeColumns()},handleForceUpdate(){this.localeUpdateKey++,this.$forceUpdate()}}},El=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},Lf={name:"LanguageToggle",mixins:[wl],data(){return{currentLocale:this.$locale()}},methods:{toggleLanguage(){const e=this.currentLocale==="ar"?"en":"ar";this.$setLocale(e),this.currentLocale=e},handleLocaleChange(e){this.currentLocale=e.detail.locale}}},Nf={class:"relative"},If=["title"],Ff={class:"font-semibold"};function Mf(e,t,n,r,s,o){return Ge(),jn("div",Nf,[ye("button",{onClick:t[0]||(t[0]=(...i)=>o.toggleLanguage&&o.toggleLanguage(...i)),class:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200",title:s.currentLocale==="ar"?"Switch to English":"التبديل إلى العربية"},[t[1]||(t[1]=ye("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[ye("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})],-1)),ye("span",Ff,pt(s.currentLocale==="ar"?"EN":"ع"),1)],8,If)])}const Df=El(Lf,[["render",Mf]]),Uf={name:"App",mixins:[wl],components:{LanguageToggle:Df},data(){return{user:null}},computed:{isAuthenticated(){return!!localStorage.getItem("token")},isAdmin(){var e,t;return((e=this.user)==null?void 0:e.role)==="admin"||((t=this.user)==null?void 0:t.role)==="super_admin"},isNeighbor(){var e;return((e=this.user)==null?void 0:e.role)==="neighbor"}},created(){this.user=JSON.parse(localStorage.getItem("user")||"null")},methods:{async logout(){try{await this.$axios.post("/logout")}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("token"),localStorage.removeItem("user"),this.user=null,this.$router.push("/login")}}}},kf={class:"min-h-screen bg-gray-100"},Bf={class:"bg-white shadow-lg mb-6"},jf={class:"max-w-7xl mx-auto px-4"},$f={class:"flex justify-between h-16 items-center"},Hf={class:"flex items-center"},qf={class:"text-xl font-bold text-gray-800"},Vf={key:0,class:"ml-10 flex items-center space-x-4"},Kf={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"};function Wf(e,t,n,r,s,o){var u;const i=Or("router-link"),l=Or("language-toggle"),a=Or("router-view");return Ge(),jn("div",kf,[ye("nav",Bf,[ye("div",jf,[ye("div",$f,[ye("div",Hf,[ye("h1",qf,pt(e.$t("app_name")),1),o.isAuthenticated?(Ge(),jn("div",Vf,[o.isAdmin?(Ge(),Vt(i,{key:0,to:"/admin",class:"text-gray-600 hover:text-gray-900","active-class":"text-indigo-600"},{default:Un(()=>[Hn(pt(e.$t("admin_dashboard")),1)]),_:1})):Ir("",!0),o.isNeighbor?(Ge(),Vt(i,{key:1,to:"/neighbor",class:"text-gray-600 hover:text-gray-900","active-class":"text-indigo-600"},{default:Un(()=>[Hn(pt(e.$t("neighbor_dashboard")),1)]),_:1})):Ir("",!0)])):Ir("",!0)]),ye("div",{class:kt(["flex items-center",e.$isRTL()?"space-x-reverse space-x-4":"space-x-4"])},[he(l),o.isAuthenticated?(Ge(),jn(Ce,{key:0},[ye("span",{class:kt(["text-gray-600",e.$isRTL()?"ml-4":"mr-4"])},pt((u=s.user)==null?void 0:u.name),3),ye("button",{onClick:t[0]||(t[0]=(...c)=>o.logout&&o.logout(...c)),class:"text-red-600 hover:text-red-800"},pt(e.$t("logout")),1)],64)):(Ge(),Vt(i,{key:1,to:"/login",class:kt(["text-gray-600 hover:text-gray-900",e.$isRTL()?"ml-4":"mr-4"]),"active-class":"text-indigo-600"},{default:Un(()=>[Hn(pt(e.$t("login")),1)]),_:1},8,["class"]))],2)])])]),ye("main",Kf,[he(a)])])}const zf=El(Uf,[["render",Wf]]),Gf="modulepreload",Jf=function(e){return"/build/"+e},xo={},ce=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){let i=function(u){return Promise.all(u.map(c=>Promise.resolve(c).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),a=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));s=i(n.map(u=>{if(u=Jf(u),u in xo)return;xo[u]=!0;const c=u.endsWith(".css"),f=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${f}`))return;const p=document.createElement("link");if(p.rel=c?"stylesheet":Gf,c||(p.as="script"),p.crossOrigin="",p.href=u,a&&p.setAttribute("nonce",a),document.head.appendChild(p),c)return new Promise((g,y)=>{p.addEventListener("load",g),p.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${u}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return s.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Mt=typeof document<"u";function xl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Xf(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&xl(e.default)}const z=Object.assign;function Dr(e,t){const n={};for(const r in t){const s=t[r];n[r]=Ue(s)?s.map(e):e(s)}return n}const fn=()=>{},Ue=Array.isArray,vl=/#/g,Yf=/&/g,Qf=/\//g,Zf=/=/g,ed=/\?/g,Sl=/\+/g,td=/%5B/g,nd=/%5D/g,Rl=/%5E/g,rd=/%60/g,Al=/%7B/g,sd=/%7C/g,Tl=/%7D/g,od=/%20/g;function As(e){return encodeURI(""+e).replace(sd,"|").replace(td,"[").replace(nd,"]")}function id(e){return As(e).replace(Al,"{").replace(Tl,"}").replace(Rl,"^")}function ts(e){return As(e).replace(Sl,"%2B").replace(od,"+").replace(vl,"%23").replace(Yf,"%26").replace(rd,"`").replace(Al,"{").replace(Tl,"}").replace(Rl,"^")}function ld(e){return ts(e).replace(Zf,"%3D")}function ad(e){return As(e).replace(vl,"%23").replace(ed,"%3F")}function cd(e){return e==null?"":ad(e).replace(Qf,"%2F")}function En(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const ud=/\/$/,fd=e=>e.replace(ud,"");function Ur(e,t,n="/"){let r,s={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(r=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),s=e(o)),l>-1&&(r=r||t.slice(0,l),i=t.slice(l,t.length)),r=md(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:En(i)}}function dd(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function vo(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function hd(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&zt(t.matched[r],n.matched[s])&&Cl(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function zt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Cl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!pd(e[n],t[n]))return!1;return!0}function pd(e,t){return Ue(e)?So(e,t):Ue(t)?So(t,e):e===t}function So(e,t){return Ue(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function md(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,l;for(i=0;i<r.length;i++)if(l=r[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const ht={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var xn;(function(e){e.pop="pop",e.push="push"})(xn||(xn={}));var dn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(dn||(dn={}));function gd(e){if(!e)if(Mt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),fd(e)}const _d=/^[^#]+#/;function yd(e,t){return e.replace(_d,"#")+t}function bd(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const yr=()=>({left:window.scrollX,top:window.scrollY});function wd(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=bd(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ro(e,t){return(history.state?history.state.position-t:-1)+e}const ns=new Map;function Ed(e,t){ns.set(e,t)}function xd(e){const t=ns.get(e);return ns.delete(e),t}let vd=()=>location.protocol+"//"+location.host;function Ol(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let l=s.includes(e.slice(o))?e.slice(o).length:1,a=s.slice(l);return a[0]!=="/"&&(a="/"+a),vo(a,"")}return vo(n,e)+r+s}function Sd(e,t,n,r){let s=[],o=[],i=null;const l=({state:p})=>{const g=Ol(e,location),y=n.value,v=t.value;let S=0;if(p){if(n.value=g,t.value=p,i&&i===y){i=null;return}S=v?p.position-v.position:0}else r(g);s.forEach(P=>{P(n.value,y,{delta:S,type:xn.pop,direction:S?S>0?dn.forward:dn.back:dn.unknown})})};function a(){i=n.value}function u(p){s.push(p);const g=()=>{const y=s.indexOf(p);y>-1&&s.splice(y,1)};return o.push(g),g}function c(){const{history:p}=window;p.state&&p.replaceState(z({},p.state,{scroll:yr()}),"")}function f(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function Ao(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?yr():null}}function Rd(e){const{history:t,location:n}=window,r={value:Ol(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,u,c){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:vd()+e+a;try{t[c?"replaceState":"pushState"](u,"",p),s.value=u}catch(g){console.error(g),n[c?"replace":"assign"](p)}}function i(a,u){const c=z({},t.state,Ao(s.value.back,a,s.value.forward,!0),u,{position:s.value.position});o(a,c,!0),r.value=a}function l(a,u){const c=z({},s.value,t.state,{forward:a,scroll:yr()});o(c.current,c,!0);const f=z({},Ao(r.value,a,null),{position:c.position+1},u);o(a,f,!1),r.value=a}return{location:r,state:s,push:l,replace:i}}function Ad(e){e=gd(e);const t=Rd(e),n=Sd(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=z({location:"",base:e,go:r,createHref:yd.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function Td(e){return typeof e=="string"||e&&typeof e=="object"}function Pl(e){return typeof e=="string"||typeof e=="symbol"}const Ll=Symbol("");var To;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(To||(To={}));function Gt(e,t){return z(new Error,{type:e,[Ll]:!0},t)}function et(e,t){return e instanceof Error&&Ll in e&&(t==null||!!(e.type&t))}const Co="[^/]+?",Cd={sensitive:!1,strict:!1,start:!0,end:!0},Od=/[.+*?^${}()[\]/\\]/g;function Pd(e,t){const n=z({},Cd,t),r=[];let s=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(s+="/");for(let f=0;f<u.length;f++){const p=u[f];let g=40+(n.sensitive?.25:0);if(p.type===0)f||(s+="/"),s+=p.value.replace(Od,"\\$&"),g+=40;else if(p.type===1){const{value:y,repeatable:v,optional:S,regexp:P}=p;o.push({name:y,repeatable:v,optional:S});const C=P||Co;if(C!==Co){g+=10;try{new RegExp(`(${C})`)}catch(F){throw new Error(`Invalid custom RegExp for param "${y}" (${C}): `+F.message)}}let I=v?`((?:${C})(?:/(?:${C}))*)`:`(${C})`;f||(I=S&&u.length<2?`(?:/${I})`:"/"+I),S&&(I+="?"),s+=I,g+=20,S&&(g+=-8),v&&(g+=-20),C===".*"&&(g+=-50)}c.push(g)}r.push(c)}if(n.strict&&n.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function l(u){const c=u.match(i),f={};if(!c)return null;for(let p=1;p<c.length;p++){const g=c[p]||"",y=o[p-1];f[y.name]=g&&y.repeatable?g.split("/"):g}return f}function a(u){let c="",f=!1;for(const p of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const g of p)if(g.type===0)c+=g.value;else if(g.type===1){const{value:y,repeatable:v,optional:S}=g,P=y in u?u[y]:"";if(Ue(P)&&!v)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const C=Ue(P)?P.join("/"):P;if(!C)if(S)p.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);c+=C}}return c||"/"}return{re:i,score:r,keys:o,parse:l,stringify:a}}function Ld(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Nl(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=Ld(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(Oo(r))return 1;if(Oo(s))return-1}return s.length-r.length}function Oo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Nd={type:0,value:""},Id=/[a-zA-Z0-9_]/;function Fd(e){if(!e)return[[]];if(e==="/")return[[Nd]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${u}": ${g}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let l=0,a,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),i()):a===":"?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:a==="("?n=2:Id.test(a)?p():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),s}function Md(e,t,n){const r=Pd(Fd(e.path),n),s=z(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function Dd(e,t){const n=[],r=new Map;t=Io({strict:!1,end:!0,sensitive:!1},t);function s(f){return r.get(f)}function o(f,p,g){const y=!g,v=Lo(f);v.aliasOf=g&&g.record;const S=Io(t,f),P=[v];if("alias"in f){const F=typeof f.alias=="string"?[f.alias]:f.alias;for(const H of F)P.push(Lo(z({},v,{components:g?g.record.components:v.components,path:H,aliasOf:g?g.record:v})))}let C,I;for(const F of P){const{path:H}=F;if(p&&H[0]!=="/"){const ee=p.record.path,K=ee[ee.length-1]==="/"?"":"/";F.path=p.record.path+(H&&K+H)}if(C=Md(F,p,S),g?g.alias.push(C):(I=I||C,I!==C&&I.alias.push(C),y&&f.name&&!No(C)&&i(f.name)),Il(C)&&a(C),v.children){const ee=v.children;for(let K=0;K<ee.length;K++)o(ee[K],C,g&&g.children[K])}g=g||C}return I?()=>{i(I)}:fn}function i(f){if(Pl(f)){const p=r.get(f);p&&(r.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&r.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const p=Bd(f,n);n.splice(p,0,f),f.record.name&&!No(f)&&r.set(f.record.name,f)}function u(f,p){let g,y={},v,S;if("name"in f&&f.name){if(g=r.get(f.name),!g)throw Gt(1,{location:f});S=g.record.name,y=z(Po(p.params,g.keys.filter(I=>!I.optional).concat(g.parent?g.parent.keys.filter(I=>I.optional):[]).map(I=>I.name)),f.params&&Po(f.params,g.keys.map(I=>I.name))),v=g.stringify(y)}else if(f.path!=null)v=f.path,g=n.find(I=>I.re.test(v)),g&&(y=g.parse(v),S=g.record.name);else{if(g=p.name?r.get(p.name):n.find(I=>I.re.test(p.path)),!g)throw Gt(1,{location:f,currentLocation:p});S=g.record.name,y=z({},p.params,f.params),v=g.stringify(y)}const P=[];let C=g;for(;C;)P.unshift(C.record),C=C.parent;return{name:S,path:v,params:y,matched:P,meta:kd(P)}}e.forEach(f=>o(f));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:s}}function Po(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Lo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Ud(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Ud(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function No(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function kd(e){return e.reduce((t,n)=>z(t,n.meta),{})}function Io(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Bd(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Nl(e,t[o])<0?r=o:n=o+1}const s=jd(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function jd(e){let t=e;for(;t=t.parent;)if(Il(t)&&Nl(e,t)===0)return t}function Il({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function $d(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(Sl," "),i=o.indexOf("="),l=En(i<0?o:o.slice(0,i)),a=i<0?null:En(o.slice(i+1));if(l in t){let u=t[l];Ue(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function Fo(e){let t="";for(let n in e){const r=e[n];if(n=ld(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ue(r)?r.map(o=>o&&ts(o)):[r&&ts(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Hd(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Ue(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const qd=Symbol(""),Mo=Symbol(""),Ts=Symbol(""),Fl=Symbol(""),rs=Symbol("");function rn(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function _t(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((l,a)=>{const u=p=>{p===!1?a(Gt(4,{from:n,to:t})):p instanceof Error?a(p):Td(p)?a(Gt(2,{from:t,to:p})):(i&&r.enterCallbacks[s]===i&&typeof p=="function"&&i.push(p),l())},c=o(()=>e.call(r&&r.instances[s],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(p=>a(p))})}function kr(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(xl(a)){const c=(a.__vccOpts||a)[t];c&&o.push(_t(c,n,r,i,l,s))}else{let u=a();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=Xf(c)?c.default:c;i.mods[l]=c,i.components[l]=f;const g=(f.__vccOpts||f)[t];return g&&_t(g,n,r,i,l,s)()}))}}return o}function Do(e){const t=ot(Ts),n=ot(Fl),r=Fe(()=>{const a=jt(e.to);return t.resolve(a)}),s=Fe(()=>{const{matched:a}=r.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const p=f.findIndex(zt.bind(null,c));if(p>-1)return p;const g=Uo(a[u-2]);return u>1&&Uo(c)===g&&f[f.length-1].path!==g?f.findIndex(zt.bind(null,a[u-2])):p}),o=Fe(()=>s.value>-1&&Gd(n.params,r.value.params)),i=Fe(()=>s.value>-1&&s.value===n.matched.length-1&&Cl(n.params,r.value.params));function l(a={}){if(zd(a)){const u=t[jt(e.replace)?"replace":"push"](jt(e.to)).catch(fn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:r,href:Fe(()=>r.value.href),isActive:o,isExactActive:i,navigate:l}}function Vd(e){return e.length===1?e[0]:e}const Kd=$i({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Do,setup(e,{slots:t}){const n=dr(Do(e)),{options:r}=ot(Ts),s=Fe(()=>({[ko(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[ko(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Vd(t.default(n));return e.custom?o:_l("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),Wd=Kd;function zd(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Gd(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Ue(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function Uo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const ko=(e,t,n)=>e??t??n,Jd=$i({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=ot(rs),s=Fe(()=>e.route||r.value),o=ot(Mo,0),i=Fe(()=>{let u=jt(o);const{matched:c}=s.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),l=Fe(()=>s.value.matched[i.value]);kn(Mo,Fe(()=>i.value+1)),kn(qd,l),kn(rs,s);const a=Vc();return Bn(()=>[a.value,l.value,e.name],([u,c,f],[p,g,y])=>{c&&(c.instances[f]=u,g&&g!==c&&u&&u===p&&(c.leaveGuards.size||(c.leaveGuards=g.leaveGuards),c.updateGuards.size||(c.updateGuards=g.updateGuards))),u&&c&&(!g||!zt(c,g)||!p)&&(c.enterCallbacks[f]||[]).forEach(v=>v(u))},{flush:"post"}),()=>{const u=s.value,c=e.name,f=l.value,p=f&&f.components[c];if(!p)return Bo(n.default,{Component:p,route:u});const g=f.props[c],y=g?g===!0?u.params:typeof g=="function"?g(u):g:null,S=_l(p,z({},y,t,{onVnodeUnmounted:P=>{P.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return Bo(n.default,{Component:S,route:u})||S}}});function Bo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Xd=Jd;function Yd(e){const t=Dd(e.routes,e),n=e.parseQuery||$d,r=e.stringifyQuery||Fo,s=e.history,o=rn(),i=rn(),l=rn(),a=Kc(ht);let u=ht;Mt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Dr.bind(null,w=>""+w),f=Dr.bind(null,cd),p=Dr.bind(null,En);function g(w,M){let L,D;return Pl(w)?(L=t.getRecordMatcher(w),D=M):D=w,t.addRoute(D,L)}function y(w){const M=t.getRecordMatcher(w);M&&t.removeRoute(M)}function v(){return t.getRoutes().map(w=>w.record)}function S(w){return!!t.getRecordMatcher(w)}function P(w,M){if(M=z({},M||a.value),typeof w=="string"){const m=Ur(n,w,M.path),b=t.resolve({path:m.path},M),x=s.createHref(m.fullPath);return z(m,b,{params:p(b.params),hash:En(m.hash),redirectedFrom:void 0,href:x})}let L;if(w.path!=null)L=z({},w,{path:Ur(n,w.path,M.path).path});else{const m=z({},w.params);for(const b in m)m[b]==null&&delete m[b];L=z({},w,{params:f(m)}),M.params=f(M.params)}const D=t.resolve(L,M),Q=w.hash||"";D.params=c(p(D.params));const d=dd(r,z({},w,{hash:id(Q),path:D.path})),h=s.createHref(d);return z({fullPath:d,hash:Q,query:r===Fo?Hd(w.query):w.query||{}},D,{redirectedFrom:void 0,href:h})}function C(w){return typeof w=="string"?Ur(n,w,a.value.path):z({},w)}function I(w,M){if(u!==w)return Gt(8,{from:M,to:w})}function F(w){return K(w)}function H(w){return F(z(C(w),{replace:!0}))}function ee(w){const M=w.matched[w.matched.length-1];if(M&&M.redirect){const{redirect:L}=M;let D=typeof L=="function"?L(w):L;return typeof D=="string"&&(D=D.includes("?")||D.includes("#")?D=C(D):{path:D},D.params={}),z({query:w.query,hash:w.hash,params:D.path!=null?{}:w.params},D)}}function K(w,M){const L=u=P(w),D=a.value,Q=w.state,d=w.force,h=w.replace===!0,m=ee(L);if(m)return K(z(C(m),{state:typeof m=="object"?z({},Q,m.state):Q,force:d,replace:h}),M||L);const b=L;b.redirectedFrom=M;let x;return!d&&hd(r,D,L)&&(x=Gt(16,{to:b,from:D}),$e(D,D,!0,!1)),(x?Promise.resolve(x):Be(b,D)).catch(E=>et(E)?et(E,2)?E:dt(E):W(E,b,D)).then(E=>{if(E){if(et(E,2))return K(z({replace:h},C(E.to),{state:typeof E.to=="object"?z({},Q,E.to.state):Q,force:d}),M||b)}else E=Et(b,D,!0,h,Q);return ft(b,D,E),E})}function ge(w,M){const L=I(w,M);return L?Promise.reject(L):Promise.resolve()}function Ie(w){const M=Nt.values().next().value;return M&&typeof M.runWithContext=="function"?M.runWithContext(w):w()}function Be(w,M){let L;const[D,Q,d]=Qd(w,M);L=kr(D.reverse(),"beforeRouteLeave",w,M);for(const m of D)m.leaveGuards.forEach(b=>{L.push(_t(b,w,M))});const h=ge.bind(null,w,M);return L.push(h),Pe(L).then(()=>{L=[];for(const m of o.list())L.push(_t(m,w,M));return L.push(h),Pe(L)}).then(()=>{L=kr(Q,"beforeRouteUpdate",w,M);for(const m of Q)m.updateGuards.forEach(b=>{L.push(_t(b,w,M))});return L.push(h),Pe(L)}).then(()=>{L=[];for(const m of d)if(m.beforeEnter)if(Ue(m.beforeEnter))for(const b of m.beforeEnter)L.push(_t(b,w,M));else L.push(_t(m.beforeEnter,w,M));return L.push(h),Pe(L)}).then(()=>(w.matched.forEach(m=>m.enterCallbacks={}),L=kr(d,"beforeRouteEnter",w,M,Ie),L.push(h),Pe(L))).then(()=>{L=[];for(const m of i.list())L.push(_t(m,w,M));return L.push(h),Pe(L)}).catch(m=>et(m,8)?m:Promise.reject(m))}function ft(w,M,L){l.list().forEach(D=>Ie(()=>D(w,M,L)))}function Et(w,M,L,D,Q){const d=I(w,M);if(d)return d;const h=M===ht,m=Mt?history.state:{};L&&(D||h?s.replace(w.fullPath,z({scroll:h&&m&&m.scroll},Q)):s.push(w.fullPath,Q)),a.value=w,$e(w,M,L,h),dt()}let je;function Qt(){je||(je=s.listen((w,M,L)=>{if(!On.listening)return;const D=P(w),Q=ee(D);if(Q){K(z(Q,{replace:!0,force:!0}),D).catch(fn);return}u=D;const d=a.value;Mt&&Ed(Ro(d.fullPath,L.delta),yr()),Be(D,d).catch(h=>et(h,12)?h:et(h,2)?(K(z(C(h.to),{force:!0}),D).then(m=>{et(m,20)&&!L.delta&&L.type===xn.pop&&s.go(-1,!1)}).catch(fn),Promise.reject()):(L.delta&&s.go(-L.delta,!1),W(h,D,d))).then(h=>{h=h||Et(D,d,!1),h&&(L.delta&&!et(h,8)?s.go(-L.delta,!1):L.type===xn.pop&&et(h,20)&&s.go(-1,!1)),ft(D,d,h)}).catch(fn)}))}let Pt=rn(),ie=rn(),Y;function W(w,M,L){dt(w);const D=ie.list();return D.length?D.forEach(Q=>Q(w,M,L)):console.error(w),Promise.reject(w)}function Qe(){return Y&&a.value!==ht?Promise.resolve():new Promise((w,M)=>{Pt.add([w,M])})}function dt(w){return Y||(Y=!w,Qt(),Pt.list().forEach(([M,L])=>w?L(w):M()),Pt.reset()),w}function $e(w,M,L,D){const{scrollBehavior:Q}=e;if(!Mt||!Q)return Promise.resolve();const d=!L&&xd(Ro(w.fullPath,0))||(D||!L)&&history.state&&history.state.scroll||null;return ws().then(()=>Q(w,M,d)).then(h=>h&&wd(h)).catch(h=>W(h,w,M))}const we=w=>s.go(w);let Lt;const Nt=new Set,On={currentRoute:a,listening:!0,addRoute:g,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:S,getRoutes:v,resolve:P,options:e,push:F,replace:H,go:we,back:()=>we(-1),forward:()=>we(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:ie.add,isReady:Qe,install(w){const M=this;w.component("RouterLink",Wd),w.component("RouterView",Xd),w.config.globalProperties.$router=M,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>jt(a)}),Mt&&!Lt&&a.value===ht&&(Lt=!0,F(s.location).catch(Q=>{}));const L={};for(const Q in ht)Object.defineProperty(L,Q,{get:()=>a.value[Q],enumerable:!0});w.provide(Ts,M),w.provide(Fl,Ni(L)),w.provide(rs,a);const D=w.unmount;Nt.add(w),w.unmount=function(){Nt.delete(w),Nt.size<1&&(u=ht,je&&je(),je=null,a.value=ht,Lt=!1,Y=!1),D()}}};function Pe(w){return w.reduce((M,L)=>M.then(()=>Ie(L)),Promise.resolve())}return On}function Qd(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(u=>zt(u,l))?r.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>zt(u,a))||s.push(a))}return[n,r,s]}const Zd=[{path:"/login",name:"Login",component:()=>ce(()=>import("./Login-DMlg1Qfa.js"),[]),meta:{requiresGuest:!0}},{path:"/register",name:"Register",component:()=>ce(()=>import("./Register-CxmByzDh.js"),[]),meta:{requiresGuest:!0}},{path:"/registration-success",name:"RegistrationSuccess",component:()=>ce(()=>import("./RegistrationSuccess-B8WNcaaY.js"),[]),meta:{requiresAuth:!0}},{path:"/admin",name:"AdminDashboard",component:()=>ce(()=>import("./AdminDashboard-BMtttaIa.js"),__vite__mapDeps([0,1,2])),meta:{requiresAuth:!0,role:["admin","super_admin"]}},{path:"/neighbor",name:"NeighborDashboard",component:()=>ce(()=>import("./NeighborDashboard-CqbBwD0w.js"),__vite__mapDeps([3,1,2,4])),meta:{requiresAuth:!0,role:"neighbor"}},{path:"/admin/users",name:"admin.users",component:()=>ce(()=>import("./UserManagement-G97gsK7c.js"),__vite__mapDeps([5,1,2,6,4])),meta:{requiresAuth:!0,role:["admin","super_admin"]}},{path:"/admin/users/create",name:"admin.users.create",component:()=>ce(()=>import("./CreateUser-CFsZk7kt.js"),__vite__mapDeps([7,1,6,4])),meta:{requiresAuth:!0,role:["admin","super_admin"]}},{path:"/admin/my-building",name:"admin.my-building",component:()=>ce(()=>import("./MyBuilding-2VWMq-A2.js"),__vite__mapDeps([8,1,9,4])),meta:{requiresAuth:!0,role:"admin"}},{path:"/admin/expenses",name:"admin.expenses",component:()=>ce(()=>import("./ExpenseManagement-D0R5vrQp.js"),__vite__mapDeps([10,1,2,11,4])),meta:{requiresAuth:!0,role:["admin","super_admin"]}},{path:"/admin/expenses/create",name:"admin.expenses.create",component:()=>ce(()=>import("./CreateExpense-4z0eaws0.js"),__vite__mapDeps([12,1,11,4])),meta:{requiresAuth:!0,role:["admin","super_admin"]}},{path:"/admin/incomes",name:"IncomeManagement",component:()=>ce(()=>import("./IncomeManagement-CylQxRx_.js"),__vite__mapDeps([13,1,2,14,4])),meta:{requiresAuth:!0,role:["admin","super_admin"]}},{path:"/admin/incomes/create",name:"CreateIncome",component:()=>ce(()=>import("./CreateIncome-CA-TnWaY.js"),__vite__mapDeps([15,1,14,4])),meta:{requiresAuth:!0,role:["admin","super_admin"]}},{path:"/admin/buildings",name:"admin.buildings",component:()=>ce(()=>import("./BuildingManagement-Dv9thyRK.js"),__vite__mapDeps([16,1,2,9,4])),meta:{requiresAuth:!0,role:"super_admin"}},{path:"/admin/buildings/create",name:"admin.buildings.create",component:()=>ce(()=>import("./CreateBuilding-27nUgw7_.js"),__vite__mapDeps([17,1,9,4])),meta:{requiresAuth:!0,role:"super_admin"}},{path:"/admin/buildings/:id/edit",name:"admin.buildings.edit",component:()=>ce(()=>import("./EditBuilding-B2e1VlX9.js"),__vite__mapDeps([18,1,9,4])),meta:{requiresAuth:!0,role:"super_admin"}},{path:"/",redirect:"/login"}],br=Yd({history:Ad("/lajnet-amara/"),routes:Zd});br.beforeEach((e,t,n)=>{const r=localStorage.getItem("token"),s=JSON.parse(localStorage.getItem("user")||"null");if(console.log("Router guard - navigating to:",e.name,e.path),console.log("Router guard - user:",s),console.log("Router guard - token exists:",!!r),e.meta.requiresAuth){if(!r){console.log("Router guard - no token, redirecting to login"),n({name:"Login"});return}if(e.meta.role){if(e.meta.role==="super_admin"&&(s==null?void 0:s.role)!=="super_admin"){console.log("Router guard - not super admin, redirecting. Required:",e.meta.role,"User role:",s==null?void 0:s.role),n({name:(s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="super_admin"?"AdminDashboard":"NeighborDashboard"});return}else if(Array.isArray(e.meta.role)&&!e.meta.role.includes(s==null?void 0:s.role)){console.log("Router guard - role not allowed, redirecting. Required:",e.meta.role,"User role:",s==null?void 0:s.role),n({name:(s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="super_admin"?"AdminDashboard":"NeighborDashboard"});return}else if(!Array.isArray(e.meta.role)&&e.meta.role==="admin"&&(s==null?void 0:s.role)!=="admin"&&(s==null?void 0:s.role)!=="super_admin"){console.log("Router guard - not admin, redirecting. Required:",e.meta.role,"User role:",s==null?void 0:s.role),n({name:"NeighborDashboard"});return}}}if(e.meta.requiresGuest&&r){console.log("Router guard - user logged in, redirecting from guest route"),n({name:(s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="super_admin"?"AdminDashboard":"NeighborDashboard"});return}console.log("Router guard - allowing navigation"),n()});br.afterEach(e=>{ce(async()=>{const{default:t}=await Promise.resolve().then(()=>th);return{default:t}},void 0).then(({default:t})=>{document.title=t.t("app_name")})});class eh{constructor(){this.currentLocale=localStorage.getItem("locale")||"ar",this.translations={},this.loadTranslations()}loadTranslations(){this.translations.ar={app_name:"لجنة العمارة",welcome:"مرحباً بكم في لجنة العمارة",login:"تسجيل الدخول",register:"التسجيل",logout:"تسجيل الخروج",dashboard:"لوحة التحكم",loading:"جاري التحميل...",save:"حفظ",cancel:"إلغاء",edit:"تعديل",delete:"حذف",create:"إنشاء",add:"إضافة",back:"رجوع",next:"التالي",previous:"السابق",search:"بحث",filter:"تصفية",actions:"الإجراءات",status:"الحالة",date:"التاريخ",amount:"المبلغ",total:"المجموع",name:"الاسم",email:"البريد الإلكتروني",password:"كلمة المرور",confirm_password:"تأكيد كلمة المرور",yes:"نعم",no:"لا",admin_dashboard:"لوحة تحكم الإدارة",neighbor_dashboard:"لوحة تحكم الجار",expenses:"المصروفات",incomes:"الإيرادات",users:"المستخدمين",buildings:"المباني",my_building:"مبناي",user_management:"إدارة المستخدمين",create_user:"إنشاء مستخدم جديد",edit_user:"تعديل المستخدم",apartment_number:"رقم الشقة",role:"الدور",building:"المبنى",admin:"مدير",neighbor:"جار",super_admin:"مدير عام",expense_management:"إدارة المصروفات",create_expense:"إنشاء مصروف جديد",edit_expense:"تعديل المصروف",expense_type:"نوع المصروف",month:"الشهر",year:"السنة",automatic:"تلقائي",manual:"يدوي",notes:"ملاحظات",income_management:"إدارة الإيرادات",create_income:"إنشاء إيراد جديد",edit_income:"تعديل الإيراد",payment_date:"تاريخ الدفع",payment_method:"طريقة الدفع",cash:"نقداً",bank_transfer:"تحويل بنكي",check:"شيك",building_management:"إدارة المباني",create_building:"إنشاء مبنى جديد",edit_building:"تعديل المبنى",building_name:"اسم المبنى",address:"العنوان",city:"المدينة",country:"البلد",postal_code:"الرمز البريدي",monthly_fee:"الرسوم الشهرية",description:"الوصف",pending:"معلق",paid:"مدفوع",received:"مستلم",cancelled:"ملغي",success:"نجح",error:"خطأ",warning:"تحذير",info:"معلومات",no_data:"لا توجد بيانات",loading_menu:"جاري تحميل القائمة...",january:"يناير",february:"فبراير",march:"مارس",april:"أبريل",may:"مايو",june:"يونيو",july:"يوليو",august:"أغسطس",september:"سبتمبر",october:"أكتوبر",november:"نوفمبر",december:"ديسمبر",required_field:"هذا الحقل مطلوب",invalid_email:"البريد الإلكتروني غير صحيح",password_mismatch:"كلمات المرور غير متطابقة",user_created:"تم إنشاء المستخدم بنجاح",user_updated:"تم تحديث المستخدم بنجاح",user_deleted:"تم حذف المستخدم بنجاح",expense_created:"تم إنشاء المصروف بنجاح",expense_updated:"تم تحديث المصروف بنجاح",expense_deleted:"تم حذف المصروف بنجاح",income_created:"تم إنشاء الإيراد بنجاح",income_updated:"تم تحديث الإيراد بنجاح",income_deleted:"تم حذف الإيراد بنجاح",building_created:"تم إنشاء المبنى بنجاح",building_updated:"تم تحديث المبنى بنجاح",building_deleted:"تم حذف المبنى بنجاح",outstanding_balance:"الرصيد المستحق",total_expenses:"إجمالي المصروفات",total_income:"إجمالي الإيرادات",financial_summary:"الملخص المالي",recent_expenses:"المصروفات الأخيرة",recent_income:"الإيرادات الأخيرة",view_all:"عرض الكل",add_expense:"إضافة مصروف",add_income:"إضافة إيراد",add_user:"إضافة مستخدم",add_building:"إضافة مبنى",select:"اختر",all:"الكل",type:"النوع",user:"المستخدم",building_information:"معلومات المبنى",not_specified:"غير محدد",monthly_expense_generation:"إنشاء المصروفات الشهرية",generate_monthly_expenses_description:"إنشاء المصروفات الشهرية لجميع الجيران في مبناك.",each_neighbor_charged:"سيتم تحصيل",generate_monthly_expenses:"إنشاء المصروفات الشهرية",generating:"جاري الإنشاء...",no_building_assigned:"لا يوجد مبنى مخصص لحسابك.",record_new_income:"تسجيل إيراد جديد",apply_filters:"تطبيق المرشحات",from_date:"من تاريخ",to_date:"إلى تاريخ",last_30_days:"آخر 30 يوم",this_month:"هذا الشهر",total_income:"إجمالي الإيرادات",income_records:"سجلات الإيرادات",method:"الطريقة",payment_date:"تاريخ الدفع",apartment:"الشقة",neighbor:"الجار",showing_results:"عرض النتائج",to:"إلى",of:"من",results:"نتيجة",next:"التالي",previous:"السابق",all_types:"جميع الأنواع",all_months:"جميع الشهور",building_services:"خدمات المبنى",building_electricity:"كهرباء المبنى",personal_electricity:"كهرباء شخصية",water:"مياه",other:"أخرى",overdue:"متأخر",auto:"تلقائي",expense_records:"سجلات المصروفات",confirm_delete_income:"هل أنت متأكد من حذف سجل الإيراد هذا؟",deleted:"تم الحذف",delete_failed:"فشل الحذف",failed_delete_income:"فشل في حذف سجل الإيراد",updated:"تم التحديث",update_failed:"فشل التحديث",error_loading_incomes:"خطأ في تحميل الإيرادات",my_financial_summary:"ملخصي المالي",total_payments:"إجمالي المدفوعات",my_expenses:"مصروفاتي",my_income_records:"سجلات إيراداتي",authentication_error:"خطأ في المصادقة",user_not_found:"المستخدم غير موجود. يرجى تسجيل الدخول مرة أخرى.",data_loaded:"تم تحميل البيانات",no_financial_records:"لم يتم العثور على سجلات مصروفات أو إيرادات لحسابك.",error_loading_dashboard:"خطأ في تحميل بيانات لوحة التحكم",failed_load_financial_data:"فشل في تحميل بياناتك المالية",total_buildings:"إجمالي المباني",total_admins:"إجمالي المديرين",total_neighbors:"إجمالي الجيران",total_users:"إجمالي المستخدمين",active_buildings:"المباني النشطة",admins:"المديرين",neighbors:"الجيران",add_admin:"إضافة مدير",neighbor_financial_summary:"ملخص الجيران المالي",loading_dashboard_data:"جاري تحميل بيانات لوحة التحكم...",created:"تاريخ الإنشاء",confirm_delete_building:'هل أنت متأكد من حذف المبنى "{name}"؟',failed_delete_building:"فشل في حذف المبنى",confirm_delete_admin:'هل أنت متأكد من حذف المدير "{name}"؟',failed_delete_admin:"فشل في حذف المدير",loading_building:"جاري تحميل المبنى...",building_not_found:"المبنى غير موجود.",failed_load_building:"فشل في تحميل المبنى",building_updated:"تم تحديث المبنى بنجاح",building_created:"تم إنشاء المبنى بنجاح",monthly_fee_description:"سيتم تحصيل هذا المبلغ من كل جار شهرياً",saving:"جاري الحفظ...",update_building:"تحديث المبنى",create_building:"إنشاء المبنى",failed_save_building:"فشل في حفظ المبنى",average_monthly_fee:"متوسط الرسوم الشهرية",no_permission:"ليس لديك صلاحية للوصول إلى هذه الصفحة.",failed_load_buildings:"فشل في تحميل المباني",building_deleted:"تم حذف المبنى بنجاح",not_specified:"غير محدد",monthly_expense_generation:"توليد المصروفات الشهرية",generate_monthly_expenses_description:"انقر على الزر أدناه لتوليد المصروفات الشهرية لجميع الجيران في مبناك.",each_neighbor_charged:"سيتم تحصيل",generating:"جاري التوليد...",generate_monthly_expenses:"توليد المصروفات الشهرية",no_building_assigned:"لم يتم تعيين مبنى لحسابك.",failed_load_building_info:"فشل في تحميل معلومات المبنى",confirm_generate_monthly_expenses:"توليد المصروفات الشهرية (₪{fee}) لجميع الجيران في مبناك لشهر {month}/{year}؟",generated:"تم التوليد",monthly_expenses_generated:"تم توليد المصروفات الشهرية بنجاح",generation_failed:"فشل التوليد",failed_generate_monthly_expenses:"فشل في توليد المصروفات الشهرية"},this.translations.en={app_name:"Building Committee",welcome:"Welcome to Building Committee",login:"Login",register:"Register",logout:"Logout",dashboard:"Dashboard",loading:"Loading...",save:"Save",cancel:"Cancel",edit:"Edit",delete:"Delete",create:"Create",add:"Add",back:"Back",next:"Next",previous:"Previous",search:"Search",filter:"Filter",actions:"Actions",status:"Status",date:"Date",amount:"Amount",total:"Total",name:"Name",email:"Email",password:"Password",confirm_password:"Confirm Password",yes:"Yes",no:"No",admin_dashboard:"Admin Dashboard",neighbor_dashboard:"Neighbor Dashboard",expenses:"Expenses",incomes:"Incomes",users:"Users",buildings:"Buildings",my_building:"My Building",user_management:"User Management",create_user:"Create New User",edit_user:"Edit User",apartment_number:"Apartment Number",role:"Role",building:"Building",admin:"Admin",neighbor:"Neighbor",super_admin:"Super Admin",expense_management:"Expense Management",create_expense:"Create New Expense",edit_expense:"Edit Expense",expense_type:"Expense Type",month:"Month",year:"Year",automatic:"Automatic",manual:"Manual",notes:"Notes",income_management:"Income Management",create_income:"Create New Income",edit_income:"Edit Income",payment_date:"Payment Date",payment_method:"Payment Method",cash:"Cash",bank_transfer:"Bank Transfer",check:"Check",building_management:"Building Management",create_building:"Create New Building",edit_building:"Edit Building",building_name:"Building Name",address:"Address",city:"City",country:"Country",postal_code:"Postal Code",monthly_fee:"Monthly Fee",description:"Description",pending:"Pending",paid:"Paid",received:"Received",cancelled:"Cancelled",success:"Success",error:"Error",warning:"Warning",info:"Info",no_data:"No data available",loading_menu:"Loading menu...",january:"January",february:"February",march:"March",april:"April",may:"May",june:"June",july:"July",august:"August",september:"September",october:"October",november:"November",december:"December",required_field:"This field is required",invalid_email:"Invalid email address",password_mismatch:"Passwords do not match",user_created:"User created successfully",user_updated:"User updated successfully",user_deleted:"User deleted successfully",expense_created:"Expense created successfully",expense_updated:"Expense updated successfully",expense_deleted:"Expense deleted successfully",income_created:"Income created successfully",income_updated:"Income updated successfully",income_deleted:"Income deleted successfully",building_created:"Building created successfully",building_updated:"Building updated successfully",building_deleted:"Building deleted successfully",outstanding_balance:"Outstanding Balance",total_expenses:"Total Expenses",total_income:"Total Income",financial_summary:"Financial Summary",recent_expenses:"Recent Expenses",recent_income:"Recent Income",view_all:"View All",add_expense:"Add Expense",add_income:"Add Income",add_user:"Add User",add_building:"Add Building",select:"Select",all:"All",type:"Type",user:"User",building_information:"Building Information",not_specified:"Not specified",monthly_expense_generation:"Monthly Expense Generation",generate_monthly_expenses_description:"Generate monthly expenses for all neighbors in your building.",each_neighbor_charged:"Each neighbor will be charged",generate_monthly_expenses:"Generate Monthly Expenses",generating:"Generating...",no_building_assigned:"No building assigned to your account.",record_new_income:"Record New Income",apply_filters:"Apply Filters",from_date:"From Date",to_date:"To Date",last_30_days:"Last 30 Days",this_month:"This Month",total_income:"Total Income",income_records:"Income Records",method:"Method",payment_date:"Payment Date",apartment:"Apartment",neighbor:"Neighbor",showing_results:"Showing",to:"to",of:"of",results:"results",next:"Next",previous:"Previous",all_types:"All Types",all_months:"All Months",building_services:"Building Services",building_electricity:"Building Electricity",personal_electricity:"Personal Electricity",water:"Water",other:"Other",overdue:"Overdue",auto:"Auto",expense_records:"Expense Records",confirm_delete_income:"Are you sure you want to delete this income record?",deleted:"Deleted",delete_failed:"Delete Failed",failed_delete_income:"Failed to delete income record",updated:"Updated",update_failed:"Update Failed",error_loading_incomes:"Error loading incomes",my_financial_summary:"My Financial Summary",total_payments:"Total Payments",my_expenses:"My Expenses",my_income_records:"My Income Records",authentication_error:"Authentication Error",user_not_found:"User not found. Please log in again.",data_loaded:"Data Loaded",no_financial_records:"No expenses or income records found for your account.",error_loading_dashboard:"Error loading dashboard data",failed_load_financial_data:"Failed to load your financial data",total_buildings:"Total Buildings",total_admins:"Total Admins",total_neighbors:"Total Neighbors",total_users:"Total Users",active_buildings:"Active Buildings",admins:"Admins",neighbors:"Neighbors",add_admin:"Add Admin",neighbor_financial_summary:"Neighbor Financial Summary",loading_dashboard_data:"Loading dashboard data...",created:"Created",confirm_delete_building:'Are you sure you want to delete building "{name}"?',failed_delete_building:"Failed to delete building",confirm_delete_admin:'Are you sure you want to delete admin "{name}"?',failed_delete_admin:"Failed to delete admin",loading_building:"Loading building...",building_not_found:"Building not found.",failed_load_building:"Failed to load building",building_updated:"Building updated successfully",building_created:"Building created successfully",monthly_fee_description:"This amount will be charged to each neighbor monthly",saving:"Saving...",update_building:"Update Building",create_building:"Create Building",failed_save_building:"Failed to save building",average_monthly_fee:"Average Monthly Fee",no_permission:"You do not have permission to access this page.",failed_load_buildings:"Failed to load buildings",building_deleted:"Building deleted successfully",not_specified:"Not specified",monthly_expense_generation:"Monthly Expense Generation",generate_monthly_expenses_description:"Click the button below to generate monthly expenses for all neighbors in your building.",each_neighbor_charged:"Each neighbor will be charged",generating:"Generating...",generate_monthly_expenses:"Generate Monthly Expenses",no_building_assigned:"No building assigned to your account.",failed_load_building_info:"Failed to load building information",confirm_generate_monthly_expenses:"Generate monthly expenses (₪{fee}) for all neighbors in your building for {month}/{year}?",generated:"Generated",monthly_expenses_generated:"Monthly expenses generated successfully",generation_failed:"Generation Failed",failed_generate_monthly_expenses:"Failed to generate monthly expenses"}}t(t,n={}){var s;const r=((s=this.translations[this.currentLocale])==null?void 0:s[t])||t;return Object.keys(n).reduce((o,i)=>o.replace(`{${i}}`,n[i]),r)}setLocale(t){this.currentLocale=t,localStorage.setItem("locale",t),document.documentElement.dir=t==="ar"?"rtl":"ltr",document.documentElement.lang=t,this.triggerGlobalUpdate(),window.dispatchEvent(new CustomEvent("localeChanged",{detail:{locale:t}}))}triggerGlobalUpdate(){window.dispatchEvent(new CustomEvent("forceUpdate"))}getLocale(){return this.currentLocale}isRTL(){return this.currentLocale==="ar"}}const nt=new eh;document.documentElement.dir=nt.isRTL()?"rtl":"ltr";document.documentElement.lang=nt.getLocale();const th=Object.freeze(Object.defineProperty({__proto__:null,default:nt},Symbol.toStringTag,{value:"Module"})),nh={install(e){e.config.globalProperties.$i18n=nt,e.config.globalProperties.$t=(t,n)=>nt.t(t,n),e.config.globalProperties.$locale=()=>nt.getLocale(),e.config.globalProperties.$isRTL=()=>nt.isRTL(),e.config.globalProperties.$setLocale=t=>nt.setLocale(t),e.provide("i18n",nt)}};se.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e});se.interceptors.response.use(e=>e,e=>{var t;return((t=e.response)==null?void 0:t.status)===401&&(localStorage.removeItem("token"),localStorage.removeItem("user"),br.push("/login")),Promise.reject(e)});const wr=Cf(zf);wr.config.globalProperties.$axios=se;wr.use(br);wr.use(nh);wr.mount("#app");export{Ce as F,El as _,ye as a,Ir as b,jn as c,Ch as d,he as e,Un as f,Hn as g,Vt as h,wl as i,Th as j,vh as k,Ah as l,Sh as m,kt as n,Ge as o,xh as p,Or as r,pt as t,Rh as v,Eh as w};
